<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 600px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .warning { background: rgba(255, 152, 0, 0.3); }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 简单测试页面</h1>
        
        <div class="status success">
            ✅ 静态HTML页面正常工作
        </div>
        
        <div id="js-status" class="status warning">
            ⏳ 正在测试JavaScript...
        </div>
        
        <div id="server-status" class="status warning">
            ⏳ 正在测试服务器连接...
        </div>
        
        <div style="margin: 20px 0;">
            <button onclick="testServer()">测试服务器</button>
            <button onclick="testVueApp()">测试Vue应用</button>
            <button onclick="window.location.href='/'">返回主应用</button>
        </div>
        
        <div id="output" style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; text-align: left; margin-top: 20px;">
            <strong>测试输出:</strong><br>
        </div>
    </div>

    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            const time = new Date().toLocaleTimeString();
            output.innerHTML += `<div>${time}: ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }
        
        // 测试JavaScript
        try {
            document.getElementById('js-status').className = 'status success';
            document.getElementById('js-status').textContent = '✅ JavaScript正常工作';
            log('✅ JavaScript测试通过');
        } catch (error) {
            document.getElementById('js-status').className = 'status error';
            document.getElementById('js-status').textContent = '❌ JavaScript错误';
            log('❌ JavaScript测试失败: ' + error.message);
        }
        
        // 测试服务器连接
        async function testServer() {
            try {
                log('🔧 开始测试服务器连接...');
                
                const response = await fetch('/');
                log('✅ 服务器响应状态: ' + response.status);
                
                if (response.ok) {
                    document.getElementById('server-status').className = 'status success';
                    document.getElementById('server-status').textContent = '✅ 服务器连接正常';
                } else {
                    document.getElementById('server-status').className = 'status error';
                    document.getElementById('server-status').textContent = '❌ 服务器响应错误';
                }
                
            } catch (error) {
                log('❌ 服务器连接失败: ' + error.message);
                document.getElementById('server-status').className = 'status error';
                document.getElementById('server-status').textContent = '❌ 服务器连接失败';
            }
        }
        
        // 测试Vue应用
        async function testVueApp() {
            try {
                log('🔧 开始测试Vue应用...');
                
                // 尝试加载Vue
                const vue = await import('vue');
                log('✅ Vue模块加载成功');
                
                // 尝试加载main.ts
                const main = await import('/src/main.ts');
                log('✅ main.ts模块加载成功');
                
                log('✅ Vue应用测试完成');
                
            } catch (error) {
                log('❌ Vue应用测试失败: ' + error.message);
                log('❌ 错误详情: ' + error.stack);
            }
        }
        
        // 自动测试服务器
        setTimeout(testServer, 1000);
        
        log('🚀 简单测试页面加载完成');
    </script>
</body>
</html>
