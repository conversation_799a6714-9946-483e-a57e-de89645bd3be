const puppeteer = require('puppeteer');

(async () => {
  let browser;
  try {
    console.log('🚀 Starting browser...');
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-web-security']
    });
    
    const page = await browser.newPage();
    
    // Listen to console messages
    page.on('console', (msg) => {
      console.log(`📝 Console ${msg.type()}: ${msg.text()}`);
    });
    
    // Listen to page errors
    page.on('pageerror', (error) => {
      console.error(`❌ Page Error: ${error.message}`);
    });
    
    // Listen to request failures
    page.on('requestfailed', (request) => {
      console.error(`❌ Request Failed: ${request.url()} - ${request.failure().errorText}`);
    });
    
    console.log('🌐 Navigating to simplified Vue app...');
    await page.goto('http://localhost:5174', { 
      waitUntil: 'networkidle0',
      timeout: 30000
    });
    
    // Wait a bit for <PERSON>ue to mount
    console.log('⏳ Waiting for Vue app to mount...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check what's actually in the DOM
    const bodyContent = await page.evaluate(() => document.body.innerHTML);
    console.log('🔍 Body HTML:', bodyContent.substring(0, 500) + '...');
    
    // Check if app element exists
    const appElement = await page.$('#app');
    console.log('📍 #app element exists:', appElement !== null);
    
    // Check if app-wrapper exists
    const appWrapperElement = await page.$('#app-wrapper');
    console.log('📍 #app-wrapper element exists:', appWrapperElement !== null);
    
    if (appWrapperElement) {
      const appContent = await page.$eval('#app-wrapper', el => el.textContent);
      console.log('✅ App content found:', appContent.substring(0, 100) + '...');
    } else {
      console.log('❌ #app-wrapper not found, checking #app content...');
      const appContent = await page.$eval('#app', el => el.innerHTML);
      console.log('📋 #app content:', appContent.substring(0, 500) + '...');
    }
    
    // Check for specific elements
    const hasTitle = await page.$('h1') !== null;
    const hasStatus = await page.$('h2') !== null;
    const hasButton = await page.$('button') !== null;
    
    console.log('📊 Element check:');
    console.log(`  Title (h1): ${hasTitle ? '✅' : '❌'}`);
    console.log(`  Status (h2): ${hasStatus ? '✅' : '❌'}`);
    console.log(`  Button: ${hasButton ? '✅' : '❌'}`);
    
    // Take a screenshot
    await page.screenshot({ path: 'simplified-vue-test.png', fullPage: true });
    console.log('📸 Screenshot saved as simplified-vue-test.png');
    
    // Test if Vue reactivity is working
    console.log('🔄 Testing Vue reactivity...');
    const initialTime = await page.$eval('p', el => el.textContent);
    console.log('Initial time text:', initialTime);
    
    // Wait 2 seconds and check if time updated
    await new Promise(resolve => setTimeout(resolve, 2000));
    const updatedTime = await page.$eval('p', el => el.textContent);
    console.log('Updated time text:', updatedTime);
    
    if (initialTime !== updatedTime) {
      console.log('✅ Vue reactivity is working - time updated!');
    } else {
      console.log('⚠️ Vue reactivity might not be working - time did not change');
    }
    
    console.log('🎉 Simplified Vue app test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
})();