<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 基础HTML测试</h1>
        <p>如果您能看到这个页面，说明：</p>
        <ul>
            <li>✅ Vite开发服务器正常工作</li>
            <li>✅ 静态文件可以正确加载</li>
            <li>✅ 浏览器可以正常渲染HTML</li>
        </ul>
        
        <div id="js-test">
            <h3>JavaScript测试</h3>
            <p id="js-status">⏳ 正在测试JavaScript...</p>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testVue()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                测试Vue加载
            </button>
            <button onclick="window.location.href='/'" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                返回主应用
            </button>
        </div>
        
        <div id="console-output" style="margin-top: 20px; background: rgba(0,0,0,0.3); padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
            <strong>控制台输出:</strong><br>
        </div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };
        
        // 测试JavaScript
        document.getElementById('js-status').textContent = '✅ JavaScript正常工作！';
        console.log('✅ JavaScript测试通过');
        
        // 测试Vue加载
        async function testVue() {
            try {
                console.log('🔧 开始测试Vue模块加载...');
                
                // 尝试加载Vue
                const { createApp } = await import('vue');
                console.log('✅ Vue模块加载成功');
                
                // 尝试加载main.ts
                const mainModule = await import('/src/main.ts');
                console.log('✅ main.ts模块加载成功');
                
            } catch (error) {
                console.error('❌ Vue模块加载失败:', error);
                alert(`Vue加载失败: ${error.message}`);
            }
        }
        
        // 错误捕获
        window.addEventListener('error', (event) => {
            console.error('❌ 页面错误:', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('❌ 未处理的Promise拒绝:', event.reason);
        });
        
        console.log('🚀 基础测试页面加载完成');
    </script>
</body>
</html>
