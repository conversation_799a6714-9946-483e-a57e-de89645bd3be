// Test script to check import issues
const puppeteer = require('puppeteer');

(async () => {
  console.log('Starting main page test...');
  
  const browser = await puppeteer.launch({
    headless: false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  });

  try {
    const page = await browser.newPage();
    
    // Capture console messages and errors
    page.on('console', msg => {
      console.log(`[CONSOLE ${msg.type()}]:`, msg.text());
    });
    
    page.on('pageerror', error => {
      console.error('[PAGE ERROR]:', error.message);
    });
    
    page.on('requestfailed', request => {
      console.error('[REQUEST FAILED]:', request.url(), request.failure().errorText);
    });
    
    // Go to main page
    console.log('Navigating to http://localhost:5174/');
    await page.goto('http://localhost:5174/', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    // Wait a bit for the app to initialize
    await page.waitForTimeout(3000);
    
    // Check if the app div has content
    const appContent = await page.$eval('#app', el => el.innerHTML.length > 0);
    console.log('App has content:', appContent);
    
    // Get the page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Check for errors in the page
    const hasErrors = await page.evaluate(() => {
      return document.body.innerText.includes('错误') || document.body.innerText.includes('Error');
    });
    console.log('Page has errors:', hasErrors);
    
    // Take a screenshot for debugging
    await page.screenshot({ 
      path: 'test_main_page.png',
      fullPage: true
    });
    console.log('Screenshot saved as test_main_page.png');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
})();