<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - Vue测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-width: 600px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status-list {
            text-align: left;
            display: inline-block;
            margin: 20px 0;
        }
        .status-list li {
            margin: 8px 0;
            padding: 5px 0;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .btn-secondary {
            background: #2196F3;
        }
        .btn-secondary:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        .loading {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid rgba(255, 193, 7, 0.5);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>🚀 {{ title }}</h1>
            <p style="font-size: 18px; margin: 20px 0;">{{ subtitle }}</p>
            
            <div style="margin: 30px 0;">
                <h3>🔧 当前状态：</h3>
                <ul class="status-list">
                    <li>✅ Vue 3 应用运行正常</li>
                    <li>✅ 前端服务器：http://localhost:5173</li>
                    <li>✅ 后端API：http://localhost:8000</li>
                    <li>✅ 响应式数据绑定正常</li>
                    <li>✅ 事件处理正常</li>
                </ul>
            </div>
            
            <div>
                <button class="btn" @click="testAPI" :disabled="loading">
                    {{ loading ? '测试中...' : '测试API连接' }}
                </button>
                <button class="btn btn-secondary" @click="reload">重新加载</button>
                <button class="btn btn-secondary" @click="goToVite">返回Vite页面</button>
            </div>
            
            <div v-if="result" class="result" :class="result.type">
                {{ result.message }}
            </div>
            
            <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                <p>⏰ 当前时间：{{ currentTime }}</p>
                <p>🔄 页面刷新次数：{{ refreshCount }}</p>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue;

        createApp({
            setup() {
                const title = ref('量化投资平台');
                const subtitle = ref('✅ 系统已成功启动！');
                const loading = ref(false);
                const result = ref(null);
                const currentTime = ref('');
                const refreshCount = ref(parseInt(localStorage.getItem('refreshCount') || '0') + 1);

                // 更新时间
                const updateTime = () => {
                    currentTime.value = new Date().toLocaleString('zh-CN');
                };

                // 测试API连接
                const testAPI = async () => {
                    loading.value = true;
                    result.value = { type: 'loading', message: '🔍 正在测试API连接...' };
                    
                    try {
                        const response = await fetch('http://localhost:8000/api/v1/health');
                        const data = await response.json();
                        result.value = {
                            type: 'success',
                            message: `✅ API连接成功！状态：${data.status}`
                        };
                    } catch (error) {
                        result.value = {
                            type: 'error',
                            message: `❌ API连接失败：${error.message}`
                        };
                    } finally {
                        loading.value = false;
                    }
                };

                // 重新加载页面
                const reload = () => {
                    window.location.reload();
                };

                // 返回Vite页面
                const goToVite = () => {
                    window.location.href = '/';
                };

                // 组件挂载时
                onMounted(() => {
                    updateTime();
                    setInterval(updateTime, 1000);
                    localStorage.setItem('refreshCount', refreshCount.value.toString());
                    
                    console.log('🎉 Vue 3 应用启动成功！');
                    console.log('📊 这是一个使用CDN Vue的独立测试页面');
                });

                return {
                    title,
                    subtitle,
                    loading,
                    result,
                    currentTime,
                    refreshCount,
                    testAPI,
                    reload,
                    goToVite
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
