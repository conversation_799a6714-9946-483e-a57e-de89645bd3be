<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端应用测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        
        .status-success { background: #4CAF50; }
        .status-error { background: #f44336; }
        .status-warning { background: #FF9800; }
        .status-info { background: #2196F3; }
        
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px;
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .iframe-container {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            background: white;
            height: 400px;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .error-log {
            color: #ff6b6b;
        }
        
        .success-log {
            color: #51cf66;
        }
        
        .info-log {
            color: #74c0fc;
        }
        
        .warning-log {
            color: #ffd43b;
        }

        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .test-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .test-success { border-left-color: #4CAF50; }
        .test-error { border-left-color: #f44336; }
        .test-warning { border-left-color: #FF9800; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🚀 量化投资平台前端测试中心</h1>
            <p>实时监控前端应用状态和性能</p>
        </div>

        <div class="card">
            <h2>📊 服务状态检查</h2>
            <div class="status-grid" id="statusGrid">
                <!-- 动态生成状态项 -->
            </div>
            <div style="margin-top: 20px;">
                <button onclick="checkAllStatus()">🔄 刷新所有状态</button>
                <button onclick="clearLogs()">🧹 清空日志</button>
                <button onclick="exportLogs()">📁 导出日志</button>
            </div>
        </div>

        <div class="card">
            <h2>🎯 快速测试</h2>
            <button onclick="testFrontendLoad()">🔧 测试前端加载</button>
            <button onclick="testBackendAPI()">⚙️ 测试后端API</button>
            <button onclick="testWebSocket()">🔌 测试WebSocket</button>
            <button onclick="testRouting()">🗺️ 测试路由</button>
            <button onclick="runFullDiagnostic()">🔍 完整诊断</button>
        </div>

        <div class="card">
            <h2>📱 前端应用预览</h2>
            <div class="iframe-container">
                <iframe 
                    id="appPreview" 
                    src="http://localhost:5176" 
                    title="前端应用预览">
                </iframe>
            </div>
            <div style="margin-top: 10px;">
                <button onclick="reloadPreview()">🔄 重新加载预览</button>
                <button onclick="openInNewTab()">🚪 在新标签页打开</button>
                <button onclick="toggleFullscreen()">🖥️ 全屏预览</button>
            </div>
        </div>

        <div class="card">
            <h2>📋 测试结果</h2>
            <div class="test-results" id="testResults">
                <!-- 动态生成测试结果 -->
            </div>
        </div>

        <div class="card">
            <h2>📝 实时日志</h2>
            <div class="log-container" id="logContainer">
                <div class="info-log">🔧 测试系统已启动，等待操作...</div>
            </div>
        </div>
    </div>

    <script>
        class FrontendTester {
            constructor() {
                this.logs = [];
                this.tests = [];
                this.statusItems = [
                    { name: '前端服务器', url: 'http://localhost:5176', key: 'frontend' },
                    { name: '后端API', url: 'http://localhost:8000/api/v1/health', key: 'backend' },
                    { name: 'Vite客户端', url: 'http://localhost:5176/@vite/client', key: 'vite' },
                    { name: '主脚本', url: 'http://localhost:5176/src/main.ts', key: 'script' }
                ];
                this.init();
            }

            init() {
                this.log('系统初始化完成', 'info');
                this.renderStatusGrid();
                this.checkAllStatus();
                
                // 监听iframe加载事件
                const iframe = document.getElementById('appPreview');
                iframe.addEventListener('load', () => {
                    this.log('预览iframe加载完成', 'success');
                    this.checkIframeContent();
                });
                
                iframe.addEventListener('error', (e) => {
                    this.log(`预览iframe加载错误: ${e.message}`, 'error');
                });
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = { timestamp, message, type };
                this.logs.push(logEntry);
                
                const logContainer = document.getElementById('logContainer');
                const logDiv = document.createElement('div');
                logDiv.className = `${type}-log`;
                logDiv.innerHTML = `[${timestamp}] ${message}`;
                logContainer.appendChild(logDiv);
                logContainer.scrollTop = logContainer.scrollHeight;
                
                // 限制日志数量
                if (this.logs.length > 100) {
                    this.logs.shift();
                    logContainer.removeChild(logContainer.firstChild);
                }
            }

            async makeRequest(url, method = 'GET') {
                try {
                    const response = await fetch(url, { 
                        method,
                        mode: 'cors',
                        cache: 'no-cache'
                    });
                    return {
                        ok: response.ok,
                        status: response.status,
                        statusText: response.statusText,
                        data: await response.text()
                    };
                } catch (error) {
                    return {
                        ok: false,
                        error: error.message
                    };
                }
            }

            renderStatusGrid() {
                const grid = document.getElementById('statusGrid');
                grid.innerHTML = '';
                
                this.statusItems.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'status-item';
                    div.innerHTML = `
                        <div class="status-icon status-info" id="status-${item.key}"></div>
                        <div>
                            <div><strong>${item.name}</strong></div>
                            <div style="font-size: 12px; opacity: 0.8;" id="status-text-${item.key}">检查中...</div>
                        </div>
                    `;
                    grid.appendChild(div);
                });
            }

            updateStatus(key, status, message = '') {
                const icon = document.getElementById(`status-${key}`);
                const text = document.getElementById(`status-text-${key}`);
                
                if (icon && text) {
                    icon.className = `status-icon status-${status}`;
                    text.textContent = message;
                }
            }

            async checkAllStatus() {
                this.log('开始检查所有服务状态...', 'info');
                
                for (const item of this.statusItems) {
                    this.updateStatus(item.key, 'info', '检查中...');
                    
                    const result = await this.makeRequest(item.url);
                    
                    if (result.ok) {
                        this.updateStatus(item.key, 'success', `✅ ${result.status}`);
                        this.log(`${item.name}: 状态正常 (${result.status})`, 'success');
                    } else {
                        this.updateStatus(item.key, 'error', `❌ ${result.error || result.status}`);
                        this.log(`${item.name}: 连接失败 - ${result.error || result.statusText}`, 'error');
                    }
                }
            }

            async testFrontendLoad() {
                this.log('开始测试前端加载...', 'info');
                
                const result = await this.makeRequest('http://localhost:5176');
                
                if (result.ok) {
                    const hasApp = result.data.includes('<div id="app">');
                    const hasScript = result.data.includes('/src/main.ts');
                    const hasVite = result.data.includes('@vite/client');
                    
                    this.addTestResult('前端HTML结构', hasApp && hasScript && hasVite ? 'success' : 'warning', 
                        `App容器: ${hasApp ? '✅' : '❌'}, 脚本: ${hasScript ? '✅' : '❌'}, Vite: ${hasVite ? '✅' : '❌'}`);
                    
                    this.log(`前端HTML检查: App容器(${hasApp}), 脚本(${hasScript}), Vite(${hasVite})`, hasApp && hasScript && hasVite ? 'success' : 'warning');
                } else {
                    this.addTestResult('前端加载', 'error', `连接失败: ${result.error}`);
                    this.log(`前端加载失败: ${result.error}`, 'error');
                }
            }

            async testBackendAPI() {
                this.log('开始测试后端API...', 'info');
                
                const result = await this.makeRequest('http://localhost:8000/api/v1/health');
                
                if (result.ok) {
                    try {
                        const data = JSON.parse(result.data);
                        this.addTestResult('后端API', 'success', `状态: ${data.status}, 路由: ${data.registered_routes}`);
                        this.log(`后端API正常: ${data.message}`, 'success');
                    } catch (e) {
                        this.addTestResult('后端API', 'warning', '响应格式异常');
                        this.log('后端API响应格式解析失败', 'warning');
                    }
                } else {
                    this.addTestResult('后端API', 'error', `连接失败: ${result.error}`);
                    this.log(`后端API失败: ${result.error}`, 'error');
                }
            }

            testWebSocket() {
                this.log('开始测试WebSocket连接...', 'info');
                
                try {
                    const ws = new WebSocket('ws://localhost:8000/api/v1/ws/market');
                    
                    ws.onopen = () => {
                        this.addTestResult('WebSocket连接', 'success', '连接成功');
                        this.log('WebSocket连接成功', 'success');
                        ws.close();
                    };
                    
                    ws.onerror = (error) => {
                        this.addTestResult('WebSocket连接', 'error', '连接失败');
                        this.log(`WebSocket连接失败: ${error.message || 'Unknown error'}`, 'error');
                    };
                    
                    ws.onclose = () => {
                        this.log('WebSocket连接已关闭', 'info');
                    };
                    
                    setTimeout(() => {
                        if (ws.readyState === WebSocket.CONNECTING) {
                            ws.close();
                            this.addTestResult('WebSocket连接', 'warning', '连接超时');
                            this.log('WebSocket连接超时', 'warning');
                        }
                    }, 5000);
                } catch (error) {
                    this.addTestResult('WebSocket连接', 'error', `创建失败: ${error.message}`);
                    this.log(`WebSocket创建失败: ${error.message}`, 'error');
                }
            }

            testRouting() {
                this.log('开始测试路由功能...', 'info');
                
                const iframe = document.getElementById('appPreview');
                if (iframe && iframe.contentWindow) {
                    try {
                        // 尝试获取iframe内的路由信息
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (iframeDoc) {
                            const hasRouter = iframeDoc.querySelector('router-view') || 
                                            (iframe.contentWindow.Vue && iframe.contentWindow.Vue.router);
                            this.addTestResult('路由系统', hasRouter ? 'success' : 'warning', 
                                hasRouter ? '路由组件已加载' : '未检测到路由组件');
                            this.log(`路由检查: ${hasRouter ? '发现路由组件' : '未发现路由组件'}`, hasRouter ? 'success' : 'warning');
                        }
                    } catch (e) {
                        this.addTestResult('路由系统', 'error', '无法访问iframe内容');
                        this.log('路由检查: 无法访问iframe内容（可能是跨域限制）', 'warning');
                    }
                } else {
                    this.addTestResult('路由系统', 'error', '预览iframe未加载');
                    this.log('路由检查: 预览iframe未加载', 'error');
                }
            }

            checkIframeContent() {
                const iframe = document.getElementById('appPreview');
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        const body = iframeDoc.body;
                        const hasContent = body && body.innerHTML.trim().length > 0;
                        const hasApp = body && body.querySelector('#app');
                        
                        this.log(`预览内容检查: 有内容(${hasContent}), App容器(${!!hasApp})`, 
                            hasContent && hasApp ? 'success' : 'warning');
                        
                        if (!hasContent || !hasApp) {
                            this.addTestResult('页面内容', 'error', '页面可能为空白');
                        } else {
                            this.addTestResult('页面内容', 'success', '内容正常显示');
                        }
                    }
                } catch (e) {
                    this.log('无法检查iframe内容（跨域限制）', 'info');
                }
            }

            async runFullDiagnostic() {
                this.log('开始运行完整诊断...', 'info');
                this.clearTestResults();
                
                await this.checkAllStatus();
                await this.testFrontendLoad();
                await this.testBackendAPI();
                this.testWebSocket();
                this.testRouting();
                
                this.log('完整诊断已完成', 'success');
            }

            addTestResult(name, status, details) {
                this.tests.push({ name, status, details, timestamp: new Date().toLocaleTimeString() });
                this.renderTestResults();
            }

            renderTestResults() {
                const container = document.getElementById('testResults');
                container.innerHTML = '';
                
                this.tests.forEach(test => {
                    const div = document.createElement('div');
                    div.className = `test-item test-${test.status}`;
                    div.innerHTML = `
                        <div><strong>${test.name}</strong></div>
                        <div style="font-size: 12px; margin: 5px 0;">${test.details}</div>
                        <div style="font-size: 10px; opacity: 0.7;">${test.timestamp}</div>
                    `;
                    container.appendChild(div);
                });
            }

            clearTestResults() {
                this.tests = [];
                this.renderTestResults();
            }

            clearLogs() {
                this.logs = [];
                document.getElementById('logContainer').innerHTML = '<div class="info-log">日志已清空</div>';
            }

            exportLogs() {
                const logText = this.logs.map(log => `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}`).join('\n');
                const blob = new Blob([logText], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `frontend-test-log-${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                this.log('日志已导出', 'success');
            }
        }

        // 全局函数
        let tester;
        
        window.addEventListener('DOMContentLoaded', () => {
            tester = new FrontendTester();
        });

        function checkAllStatus() {
            tester.checkAllStatus();
        }

        function testFrontendLoad() {
            tester.testFrontendLoad();
        }

        function testBackendAPI() {
            tester.testBackendAPI();
        }

        function testWebSocket() {
            tester.testWebSocket();
        }

        function testRouting() {
            tester.testRouting();
        }

        function runFullDiagnostic() {
            tester.runFullDiagnostic();
        }

        function clearLogs() {
            tester.clearLogs();
        }

        function exportLogs() {
            tester.exportLogs();
        }

        function reloadPreview() {
            const iframe = document.getElementById('appPreview');
            iframe.src = iframe.src;
            tester.log('预览已重新加载', 'info');
        }

        function openInNewTab() {
            window.open('http://localhost:5176', '_blank');
            tester.log('已在新标签页打开前端应用', 'info');
        }

        function toggleFullscreen() {
            const iframe = document.getElementById('appPreview');
            if (iframe.requestFullscreen) {
                iframe.requestFullscreen();
            } else if (iframe.webkitRequestFullscreen) {
                iframe.webkitRequestFullscreen();
            } else if (iframe.msRequestFullscreen) {
                iframe.msRequestFullscreen();
            }
            tester.log('已切换到全屏预览', 'info');
        }
    </script>
</body>
</html>