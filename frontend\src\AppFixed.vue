<template>
  <div id="app-wrapper">
    <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; min-height: 100vh; text-align: center;">
      <h1>🚀 量化投资平台</h1>
      <p>✅ Vue应用已成功启动！</p>
      <div style="background: rgba(255, 255, 255, 0.1); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); max-width: 600px; margin: 20px auto;">
        <h2>🎯 系统状态</h2>
        <ul style="text-align: left; display: inline-block;">
          <li>✅ Vue 3 应用运行正常</li>
          <li>✅ 前端服务器：http://localhost:5173</li>
          <li>✅ 响应式数据绑定正常</li>
          <li>✅ 事件处理正常</li>
        </ul>
        <div style="margin-top: 20px;">
          <button @click="testAPI" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">测试API连接</button>
          <button @click="reload" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">重新加载</button>
          <button @click="updateCurrentTime" style="background: #FF9800; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">更新时间</button>
        </div>
        <div v-if="apiStatus" style="margin-top: 20px; padding: 15px; border-radius: 5px;" :style="{ background: apiStatus.success ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)' }">
          {{ apiStatus.message }}
        </div>
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
          <p>⏰ 当前时间：{{ currentTime }}</p>
          <p>📄 页面状态：正常运行</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

console.log('🔧 AppFixed.vue 开始加载...')

// 响应式数据
const apiStatus = ref<any>(null)
const currentTime = ref('')
let timeInterval: number | undefined = undefined

// 更新时间函数
const updateCurrentTime = () => {
  try {
    currentTime.value = new Date().toLocaleString('zh-CN')
    console.log('⏰ 时间更新成功')
  } catch (error) {
    console.error('❌ 时间更新失败:', error)
  }
}

// 测试API连接
async function testAPI() {
  console.log('🔗 开始测试API连接...')
  try {
    const response = await fetch('http://localhost:8000/api/v1/health')
    const data = await response.json()
    apiStatus.value = {
      success: true,
      message: '✅ API连接成功！状态：' + data.status
    }
    console.log('✅ API测试成功')
  } catch (error: any) {
    apiStatus.value = {
      success: false,
      message: '❌ API连接失败：' + error.message
    }
    console.log('❌ API测试失败:', error)
  }
}

// 重新加载页面
function reload() {
  console.log('🔄 重新加载页面...')
  window.location.reload()
}

// 组件挂载时
onMounted(() => {
  console.log('🎉 AppFixed 组件开始挂载...')
  try {
    // 初始化时间
    updateCurrentTime()
    
    // 设置定时器，每秒更新时间
    timeInterval = window.setInterval(updateCurrentTime, 1000)
    
    console.log('✅ Vue 3 应用启动成功！')
    console.log('📊 AppFixed 主应用页面加载完成')
  } catch (error) {
    console.error('❌ 组件挂载过程中出错:', error)
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
    console.log('🧹 定时器已清理')
  }
})

console.log('✅ AppFixed.vue 脚本加载完成')
</script>

<style>
#app-wrapper {
  width: 100%;
  min-height: 100vh;
}
</style>