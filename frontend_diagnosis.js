/**
 * 前端问题诊断脚本
 * 测试前端应用的各种状态和功能
 */

const http = require('http');
const https = require('https');
const url = require('url');

const FRONTEND_URL = 'http://localhost:5176';
const BACKEND_URL = 'http://localhost:8000';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const urlParts = new URL(url);
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname + urlParts.search,
      method: method,
      timeout: 5000,
      headers: {
        'User-Agent': 'Frontend-Diagnosis-Script',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    };

    const protocol = urlParts.protocol === 'https:' ? https : http;
    
    const req = protocol.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          url: url
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.setTimeout(5000);
    req.end();
  });
}

async function testFrontendServer() {
  log('\n🚀 测试前端服务器状态...', 'blue');
  
  try {
    const response = await makeRequest(FRONTEND_URL);
    
    if (response.statusCode === 200) {
      log('✅ 前端服务器响应正常 (200)', 'green');
      
      // 检查HTML内容
      if (response.data.includes('<div id="app">')) {
        log('✅ HTML结构正常 - 找到app容器', 'green');
      } else {
        log('❌ HTML结构异常 - 未找到app容器', 'red');
      }
      
      // 检查主要脚本
      if (response.data.includes('src="/src/main.ts"')) {
        log('✅ 主脚本引用正常', 'green');
      } else {
        log('❌ 主脚本引用异常', 'red');
      }
      
      // 检查Vite客户端
      if (response.data.includes('@vite/client')) {
        log('✅ Vite客户端连接正常', 'green');
      } else {
        log('❌ Vite客户端连接异常', 'red');
      }
      
    } else {
      log(`❌ 前端服务器响应异常: ${response.statusCode}`, 'red');
    }
    
  } catch (error) {
    log(`❌ 前端服务器连接失败: ${error.message}`, 'red');
  }
}

async function testBackendAPI() {
  log('\n🔧 测试后端API状态...', 'blue');
  
  try {
    const response = await makeRequest(`${BACKEND_URL}/api/v1/health`);
    
    if (response.statusCode === 200) {
      log('✅ 后端API响应正常 (200)', 'green');
      
      try {
        const data = JSON.parse(response.data);
        log(`✅ API状态: ${data.status}`, 'green');
        log(`✅ 注册路由数: ${data.registered_routes}`, 'green');
        log(`✅ API版本: ${data.version}`, 'green');
      } catch (e) {
        log('❌ API响应格式异常', 'red');
      }
    } else {
      log(`❌ 后端API响应异常: ${response.statusCode}`, 'red');
    }
    
  } catch (error) {
    log(`❌ 后端API连接失败: ${error.message}`, 'red');
  }
}

async function testStaticAssets() {
  log('\n📁 测试静态资源...', 'blue');
  
  const assets = [
    '/src/main.ts',
    '/src/App.vue',
    '/src/config/index.ts',
    '/favicon.ico'
  ];
  
  for (const asset of assets) {
    try {
      const response = await makeRequest(`${FRONTEND_URL}${asset}`);
      
      if (response.statusCode === 200) {
        log(`✅ ${asset} 加载正常`, 'green');
      } else {
        log(`❌ ${asset} 加载失败 (${response.statusCode})`, 'red');
      }
    } catch (error) {
      log(`❌ ${asset} 加载错误: ${error.message}`, 'red');
    }
  }
}

async function testCORSHeaders() {
  log('\n🌐 测试CORS配置...', 'blue');
  
  try {
    const response = await makeRequest(`${BACKEND_URL}/api/v1/health`);
    const headers = response.headers;
    
    if (headers['access-control-allow-origin']) {
      log(`✅ CORS Origin: ${headers['access-control-allow-origin']}`, 'green');
    } else {
      log('⚠️ 未设置CORS Origin头', 'yellow');
    }
    
    if (headers['access-control-allow-methods']) {
      log(`✅ CORS Methods: ${headers['access-control-allow-methods']}`, 'green');
    } else {
      log('⚠️ 未设置CORS Methods头', 'yellow');
    }
    
  } catch (error) {
    log(`❌ CORS测试失败: ${error.message}`, 'red');
  }
}

async function testWebSocketConnection() {
  log('\n🔌 测试WebSocket连接...', 'blue');
  
  // 这里可以添加WebSocket连接测试
  log('ℹ️ WebSocket测试需要专门的客户端，暂时跳过', 'yellow');
}

function generateReport() {
  log('\n📋 诊断总结:', 'magenta');
  log('================================================', 'magenta');
  
  log('\n🔍 可能的问题原因分析:', 'cyan');
  log('1. JavaScript执行错误 - 检查浏览器控制台', 'yellow');
  log('2. 路由配置问题 - 检查router配置', 'yellow');
  log('3. 组件加载失败 - 检查import路径', 'yellow');
  log('4. API连接问题 - 检查网络和CORS', 'yellow');
  log('5. 资源加载失败 - 检查静态文件路径', 'yellow');
  
  log('\n🛠️ 建议的调试步骤:', 'cyan');
  log('1. 打开浏览器开发者工具', 'yellow');
  log('2. 查看Console标签页的JavaScript错误', 'yellow');
  log('3. 查看Network标签页的网络请求状态', 'yellow');
  log('4. 查看Sources标签页确认文件加载', 'yellow');
  log('5. 在Console中执行: window.__APP_DEBUG__', 'yellow');
  
  log('\n🌐 访问地址:', 'cyan');
  log(`前端应用: ${FRONTEND_URL}`, 'yellow');
  log(`后端API: ${BACKEND_URL}/api/v1/health`, 'yellow');
  log(`API文档: ${BACKEND_URL}/docs`, 'yellow');
}

async function main() {
  log('🔍 量化投资平台前端诊断工具', 'magenta');
  log('================================================', 'magenta');
  
  await testFrontendServer();
  await testBackendAPI();
  await testStaticAssets();
  await testCORSHeaders();
  await testWebSocketConnection();
  
  generateReport();
  
  log('\n✨ 诊断完成！', 'green');
}

// 执行诊断
main().catch((error) => {
  log(`❌ 诊断过程出错: ${error.message}`, 'red');
  process.exit(1);
});