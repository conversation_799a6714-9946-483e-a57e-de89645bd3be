<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Vue测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="test-app">
        <h1>{{ message }}</h1>
        <p>当前时间: {{ currentTime }}</p>
        <button @click="updateTime">更新时间</button>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue;
        
        createApp({
            setup() {
                const message = ref('🚀 Vue 3 测试应用正常运行!');
                const currentTime = ref('');
                
                const updateTime = () => {
                    currentTime.value = new Date().toLocaleString('zh-CN');
                };
                
                onMounted(() => {
                    updateTime();
                    console.log('✅ Vue应用挂载成功');
                });
                
                return {
                    message,
                    currentTime,
                    updateTime
                };
            }
        }).mount('#test-app');
        
        console.log('🔧 简单Vue测试启动');
    </script>
</body>
</html>