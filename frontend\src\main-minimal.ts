/**
 * 最小化测试版本 - 不使用router
 */
import { createApp } from 'vue'

console.log('🔧 Step 1: Vue导入成功')

// 最简单的组件
const MinimalApp = {
  template: `
    <div style="padding: 20px; font-family: Arial, sans-serif; text-align: center;">
      <h1 style="color: #409EFF;">🎉 Vue 3 应用正常运行！</h1>
      <p><strong>当前时间:</strong> {{ currentTime }}</p>
      <p><strong>应用状态:</strong> 已成功挂载到 #app</p>
      <button @click="count++" style="padding: 8px 16px; background: #409EFF; color: white; border: none; border-radius: 4px; margin: 5px;">
        点击测试 ({{ count }})
      </button>
      <div style="margin-top: 20px; padding: 15px; background: #f0f0f0; border-radius: 8px;">
        <h3>系统信息</h3>
        <ul style="list-style: none; padding: 0;">
          <li>Vue 版本: 3.x</li>
          <li>页面加载时间: {{ loadTime }}</li>
          <li>点击次数: {{ count }}</li>
        </ul>
      </div>
    </div>
  `,
  data() {
    return {
      count: 0,
      currentTime: new Date().toLocaleString(),
      loadTime: new Date().toLocaleString()
    }
  },
  mounted() {
    console.log('✅ 最小化Vue应用已成功挂载')
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
  }
}

console.log('🔧 Step 2: 最小化组件创建成功')

// 创建应用
const app = createApp(MinimalApp)
console.log('🔧 Step 3: Vue应用实例创建成功')

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('🚨 Vue错误:', err, info)
  alert(`Vue错误: ${err.message}`)
}

// 挂载应用
try {
  app.mount('#app')
  console.log('✅ 最小化应用挂载成功！')
} catch (error) {
  console.error('❌ 应用挂载失败:', error)
  
  // 显示错误页面
  document.body.innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif;">
      <h1>❌ 应用启动失败</h1>
      <p>错误信息: ${error.message}</p>
      <p>请打开浏览器控制台查看详细错误信息</p>
      <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
    </div>
  `
}

export default app
