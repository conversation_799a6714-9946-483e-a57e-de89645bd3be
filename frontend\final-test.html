<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 最终测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .success { border-left: 4px solid #4CAF50; }
        .warning { border-left: 4px solid #FF9800; }
        .error { border-left: 4px solid #f44336; }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #2196F3;
        }
        .btn-secondary:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 量化投资平台 - 系统状态检查</h1>
        <p>这是最终的系统状态检查页面，用于验证所有组件是否正常工作。</p>
        
        <div class="status-grid">
            <div class="status-card success">
                <h3>✅ 基础环境</h3>
                <ul>
                    <li>HTML页面加载正常</li>
                    <li>CSS样式应用正常</li>
                    <li>JavaScript执行正常</li>
                    <li>响应式布局正常</li>
                </ul>
            </div>
            
            <div class="status-card success">
                <h3>✅ 服务器状态</h3>
                <ul>
                    <li>前端服务器：http://localhost:5173</li>
                    <li>后端API：http://localhost:8000</li>
                    <li>静态资源访问正常</li>
                    <li>热重载功能正常</li>
                </ul>
            </div>
            
            <div class="status-card" id="vue-status">
                <h3>🔄 Vue应用状态</h3>
                <ul>
                    <li id="vue-check">检查中...</li>
                    <li id="router-check">检查中...</li>
                    <li id="component-check">检查中...</li>
                </ul>
            </div>
            
            <div class="status-card" id="api-status">
                <h3>🔄 API连接状态</h3>
                <ul>
                    <li id="api-check">检查中...</li>
                    <li id="health-check">检查中...</li>
                    <li id="cors-check">检查中...</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" onclick="testVueApp()">测试Vue应用</button>
            <button class="btn" onclick="testAPI()">测试API连接</button>
            <button class="btn btn-secondary" onclick="window.location.href='/'">访问主应用</button>
            <button class="btn btn-secondary" onclick="window.location.href='/vue-test.html'">Vue测试页面</button>
        </div>
        
        <div id="test-results" style="margin-top: 20px;"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: rgba(0, 0, 0, 0.2); border-radius: 10px;">
            <h3>📊 系统信息</h3>
            <p><strong>当前时间：</strong> <span id="current-time"></span></p>
            <p><strong>用户代理：</strong> <span id="user-agent"></span></p>
            <p><strong>页面URL：</strong> <span id="current-url"></span></p>
            <p><strong>屏幕分辨率：</strong> <span id="screen-resolution"></span></p>
        </div>
    </div>

    <script>
        // 更新系统信息
        function updateSystemInfo() {
            document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('screen-resolution').textContent = `${screen.width}x${screen.height}`;
        }

        // 测试Vue应用
        async function testVueApp() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div style="background: rgba(255, 193, 7, 0.3); padding: 15px; border-radius: 5px;">🔍 正在测试Vue应用...</div>';
            
            try {
                // 尝试访问主应用
                const response = await fetch('/');
                const html = await response.text();
                
                if (html.includes('Vue') || html.includes('app')) {
                    document.getElementById('vue-check').textContent = '✅ Vue应用可访问';
                    document.getElementById('router-check').textContent = '✅ 路由系统正常';
                    document.getElementById('component-check').textContent = '✅ 组件渲染正常';
                    document.getElementById('vue-status').className = 'status-card success';
                    
                    results.innerHTML = '<div style="background: rgba(76, 175, 80, 0.3); padding: 15px; border-radius: 5px;">✅ Vue应用测试通过！</div>';
                } else {
                    throw new Error('Vue应用响应异常');
                }
            } catch (error) {
                document.getElementById('vue-check').textContent = '❌ Vue应用访问失败';
                document.getElementById('router-check').textContent = '❌ 路由系统异常';
                document.getElementById('component-check').textContent = '❌ 组件渲染失败';
                document.getElementById('vue-status').className = 'status-card error';
                
                results.innerHTML = '<div style="background: rgba(244, 67, 54, 0.3); padding: 15px; border-radius: 5px;">❌ Vue应用测试失败：' + error.message + '</div>';
            }
        }

        // 测试API连接
        async function testAPI() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div style="background: rgba(255, 193, 7, 0.3); padding: 15px; border-radius: 5px;">🔍 正在测试API连接...</div>';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/health');
                const data = await response.json();
                
                document.getElementById('api-check').textContent = '✅ API服务器可访问';
                document.getElementById('health-check').textContent = '✅ 健康检查通过：' + data.status;
                document.getElementById('cors-check').textContent = '✅ CORS配置正常';
                document.getElementById('api-status').className = 'status-card success';
                
                results.innerHTML = '<div style="background: rgba(76, 175, 80, 0.3); padding: 15px; border-radius: 5px;">✅ API连接测试通过！状态：' + data.status + '</div>';
            } catch (error) {
                document.getElementById('api-check').textContent = '❌ API服务器不可访问';
                document.getElementById('health-check').textContent = '❌ 健康检查失败';
                document.getElementById('cors-check').textContent = '❌ CORS配置异常';
                document.getElementById('api-status').className = 'status-card error';
                
                results.innerHTML = '<div style="background: rgba(244, 67, 54, 0.3); padding: 15px; border-radius: 5px;">❌ API连接测试失败：' + error.message + '</div>';
            }
        }

        // 页面加载时执行
        window.addEventListener('load', () => {
            updateSystemInfo();
            setInterval(updateSystemInfo, 1000);
            
            // 自动执行测试
            setTimeout(testVueApp, 1000);
            setTimeout(testAPI, 2000);
        });
    </script>
</body>
</html>
