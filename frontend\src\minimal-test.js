// Minimal test to isolate the error
console.log('🔧 Minimal test script loaded')

try {
  console.log('✅ Document ready state:', document.readyState)
  console.log('✅ App element exists:', !!document.querySelector('#app'))
  
  // Try to modify the DOM directly
  const appElement = document.querySelector('#app')
  if (appElement) {
    appElement.innerHTML = '<h1 style="padding: 20px; background: green; color: white;">✅ Minimal Test Success!</h1>'
    console.log('✅ DOM manipulation successful')
  } else {
    console.error('❌ App element not found')
  }
  
  console.log('✅ Minimal test completed successfully')
  
} catch (error) {
  console.error('❌ Minimal test failed:', error)
  console.error('❌ Error stack:', error.stack)
}