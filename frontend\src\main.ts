/**
 * 应用主入口文件
 * 集成所有必要的插件和配置
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'
import { config } from '@/config'
import i18n from '@/locales'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

// 导入Element Plus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入全局组件
import GlobalComponents from '@/components'

import { setupErrorHandler } from './utils/error-handler'

// 创建Vue应用实例
const app = createApp(App)

// =======================
// Pinia状态管理配置
// =======================
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)

// =======================
// 路由配置
// =======================
app.use(router)

// =======================
// Element Plus配置
// =======================
app.use(ElementPlus)

// =======================
// 国际化配置
// =======================
app.use(i18n)

// =======================
// Element Plus图标注册
// =======================
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// =======================
// 全局组件注册
// =======================
app.use(GlobalComponents)

// =======================
// 错误处理系统初始化
// =======================
setupErrorHandler(app)

// =======================
// 全局属性配置
// =======================
app.config.globalProperties.$config = config

// =======================
// 应用启动
// =======================
app.mount('#app')

console.log(`🚀 ${config.app.name} 启动成功!`)
console.log(`🌍 运行环境: ${config.app.isDev ? '开发' : '生产'}`)
console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`)

export default app