/**
 * 极简化的量化投资平台入口文件
 */
import { createApp, ref, onMounted } from 'vue'

console.log('🔧 开始启动量化投资平台...')

// 使用直接的HTML字符串创建组件
const SimpleApp = {
  setup() {
    const currentTime = ref('')
    
    const updateTime = () => {
      currentTime.value = new Date().toLocaleString('zh-CN')
    }
    
    const reload = () => {
      window.location.reload()
    }
    
    onMounted(() => {
      console.log('🎉 Vue 3 应用启动成功！')
      updateTime()
      setInterval(updateTime, 1000)
    })
    
    return {
      currentTime,
      reload
    }
  },
  template: `
    <div id="app-wrapper" style="padding: 20px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; min-height: 100vh;">
      <h1>🚀 量化投资平台</h1>
      <p>✅ Vue应用已成功启动！</p>
      <div style="background: rgba(255, 255, 255, 0.1); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); max-width: 600px; margin: 20px auto;">
        <h2>🎯 系统状态</h2>
        <ul style="text-align: left; display: inline-block;">
          <li>✅ Vue 3 应用运行正常</li>
          <li>✅ 前端服务器：http://localhost:5174</li>
          <li>✅ 响应式数据绑定正常</li>
        </ul>
        <div style="margin-top: 20px;">
          <button @click="reload" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">重新加载</button>
        </div>
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
          <p>⏰ 当前时间：{{ currentTime }}</p>
          <p>📄 页面状态：正常运行</p>
        </div>
      </div>
    </div>
  `
}

try {
  // 创建Vue应用实例
  const app = createApp(SimpleApp)
  console.log('✅ Vue应用实例创建成功')

  // 挂载应用
  app.mount('#app')
  console.log('✅ 应用挂载成功')

  console.log('🚀 量化投资平台启动成功!')
  console.log('⏰ 启动时间:', new Date().toLocaleString('zh-CN'))

} catch (error) {
  console.error('❌ 应用启动失败:', error)
  console.error('❌ 错误堆栈:', error.stack)

  // 显示错误页面
  document.body.innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
      <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 600px;">
        <h1>❌ 量化投资平台启动失败</h1>
        <p><strong>错误信息:</strong> ${error.message}</p>
        <details style="margin-top: 15px;">
          <summary style="cursor: pointer; font-weight: bold;">查看错误堆栈</summary>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 12px; overflow: auto;">${error.stack}</pre>
        </details>
        <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px;">重新加载</button>
      </div>
    </div>
  `
}
