<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 600px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
        }
        .success { 
            background: rgba(76, 175, 80, 0.3); 
            border: 2px solid rgba(76, 175, 80, 0.5);
        }
        .error { 
            background: rgba(244, 67, 54, 0.3); 
            border: 2px solid rgba(244, 67, 54, 0.5);
        }
        .warning { 
            background: rgba(255, 152, 0, 0.3); 
            border: 2px solid rgba(255, 152, 0, 0.5);
        }
        .info { 
            background: rgba(33, 150, 243, 0.3); 
            border: 2px solid rgba(33, 150, 243, 0.5);
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: bold;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        .btn-blue { background: #2196F3; }
        .btn-blue:hover { background: #1976D2; }
        .btn-orange { background: #FF9800; }
        .btn-orange:hover { background: #F57C00; }
        #output {
            background: rgba(0,0,0,0.4);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            text-align: left;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 基础连接测试</h1>
        
        <div class="status success">
            ✅ 静态HTML页面正常加载
        </div>
        
        <div id="js-status" class="status warning">
            ⏳ 正在测试JavaScript...
        </div>
        
        <div id="server-status" class="status warning">
            ⏳ 正在测试服务器连接...
        </div>
        
        <div id="backend-status" class="status warning">
            ⏳ 正在测试后端API...
        </div>
        
        <div style="margin: 25px 0;">
            <button onclick="testAll()">🔄 重新测试所有</button>
            <button onclick="testServer()" class="btn-blue">🌐 测试服务器</button>
            <button onclick="testBackend()" class="btn-orange">🔗 测试后端</button>
            <button onclick="window.location.href='/'" class="btn-orange">🏠 返回主应用</button>
        </div>
        
        <div id="output">
            <strong>📋 测试日志:</strong><br>
        </div>
    </div>

    <script>
        const output = document.getElementById('output');
        
        function log(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const colors = {
                info: '#4fc3f7',
                success: '#4caf50',
                error: '#f44336',
                warning: '#ff9800'
            };
            const color = colors[type] || colors.info;
            output.innerHTML += `<div style="color: ${color}; margin: 2px 0;">${time}: ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }
        
        // 测试JavaScript
        function testJavaScript() {
            try {
                document.getElementById('js-status').className = 'status success';
                document.getElementById('js-status').textContent = '✅ JavaScript正常工作';
                log('✅ JavaScript测试通过', 'success');
                return true;
            } catch (error) {
                document.getElementById('js-status').className = 'status error';
                document.getElementById('js-status').textContent = '❌ JavaScript错误';
                log('❌ JavaScript测试失败: ' + error.message, 'error');
                return false;
            }
        }
        
        // 测试服务器连接
        async function testServer() {
            try {
                log('🔧 开始测试Vite开发服务器...', 'info');
                
                const response = await fetch('/basic-test.html');
                log('📡 服务器响应状态: ' + response.status, 'info');
                
                if (response.ok) {
                    document.getElementById('server-status').className = 'status success';
                    document.getElementById('server-status').textContent = '✅ Vite开发服务器连接正常';
                    log('✅ Vite开发服务器测试通过', 'success');
                    return true;
                } else {
                    document.getElementById('server-status').className = 'status error';
                    document.getElementById('server-status').textContent = '❌ 服务器响应错误: ' + response.status;
                    log('❌ 服务器响应错误: ' + response.status, 'error');
                    return false;
                }
                
            } catch (error) {
                log('❌ 服务器连接失败: ' + error.message, 'error');
                document.getElementById('server-status').className = 'status error';
                document.getElementById('server-status').textContent = '❌ 服务器连接失败';
                return false;
            }
        }
        
        // 测试后端API
        async function testBackend() {
            try {
                log('🔧 开始测试后端API连接...', 'info');
                
                // 测试健康检查端点
                const response = await fetch('http://localhost:8000/api/v1/health');
                log('📡 后端API响应状态: ' + response.status, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('backend-status').className = 'status success';
                    document.getElementById('backend-status').textContent = '✅ 后端API连接正常: ' + data.status;
                    log('✅ 后端API测试通过: ' + JSON.stringify(data), 'success');
                    return true;
                } else {
                    document.getElementById('backend-status').className = 'status error';
                    document.getElementById('backend-status').textContent = '❌ 后端API响应错误: ' + response.status;
                    log('❌ 后端API响应错误: ' + response.status, 'error');
                    return false;
                }
                
            } catch (error) {
                log('❌ 后端API连接失败: ' + error.message, 'error');
                document.getElementById('backend-status').className = 'status error';
                document.getElementById('backend-status').textContent = '❌ 后端API连接失败';
                return false;
            }
        }
        
        // 测试所有功能
        async function testAll() {
            log('🚀 开始完整系统测试...', 'info');
            
            const jsOk = testJavaScript();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const serverOk = await testServer();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const backendOk = await testBackend();
            
            if (jsOk && serverOk && backendOk) {
                log('🎉 所有测试通过！系统运行正常', 'success');
            } else {
                log('⚠️ 部分测试失败，请检查相关服务', 'warning');
            }
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            log('🚀 基础测试页面加载完成', 'info');
            setTimeout(testAll, 1000);
        });
        
        // 错误捕获
        window.addEventListener('error', (event) => {
            log('❌ 页面错误: ' + event.error.message, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log('❌ 未处理的Promise拒绝: ' + event.reason, 'error');
        });
    </script>
</body>
</html>
