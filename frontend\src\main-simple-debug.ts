// 极简调试版本入口文件
console.log('🚀 开始启动应用...')

import { createApp } from 'vue'

// 创建一个简单的内联组件用于测试
const TestApp = {
  template: `
    <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
      <h1 style="color: #42b983;">🚀 Vue 3 应用运行测试</h1>
      <p style="color: #666;">{{ message }}</p>
      <p style="color: #666;">当前时间: {{ currentTime }}</p>
      <button @click="updateTime" style="padding: 10px 20px; background: #42b983; color: white; border: none; border-radius: 5px; cursor: pointer;">
        更新时间
      </button>
    </div>
  `,
  data() {
    return {
      message: '✅ Vue 3 应用已成功启动!',
      currentTime: ''
    }
  },
  methods: {
    updateTime() {
      this.currentTime = new Date().toLocaleString('zh-CN')
      console.log('⏰ 时间已更新:', this.currentTime)
    }
  },
  mounted() {
    console.log('✅ Vue组件已挂载')
    this.updateTime()
    // 每秒更新一次时间
    setInterval(() => {
      this.updateTime()
    }, 1000)
  }
}

console.log('🔧 创建Vue应用实例...')

try {
  const app = createApp(TestApp)
  console.log('✅ Vue应用实例创建成功')
  
  app.mount('#app')
  console.log('✅ 应用成功挂载到 #app')
  
  console.log('🎉 应用启动完成!')
  
} catch (error: any) {
  console.error('❌ 应用启动失败:', error)
  console.error('错误详情:', error.message)
  console.error('错误堆栈:', error.stack)
  
  // 显示错误信息
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; background: #ffebee; color: #c62828; text-align: center; font-family: Arial, sans-serif;">
        <h1>❌ 应用启动失败</h1>
        <p><strong>错误信息:</strong> ${error.message}</p>
        <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px;">
          重新加载
        </button>
      </div>
    `
  }
}

console.log('📄 main-simple-debug.ts 执行完毕')