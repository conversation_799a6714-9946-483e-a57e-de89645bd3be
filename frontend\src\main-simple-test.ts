/**
 * 简化测试版本 - 不使用top-level await
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

console.log('🔧 Step 1: 基础导入成功')

import App from './App-simple.vue'
console.log('🔧 Step 2: App-simple.vue 导入成功')

import router from './router'
console.log('🔧 Step 3: Router 导入成功')

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
console.log('🔧 Step 4: Element Plus 导入成功')

// 导入Element Plus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
console.log('🔧 Step 5: Element Plus Icons 导入成功')

console.log('🔧 开始创建应用...')

// 创建Vue应用实例
const app = createApp(App)
console.log('🔧 Vue应用实例创建成功')

// Pinia状态管理配置
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)
console.log('🔧 Pinia配置成功')

// 路由配置
app.use(router)
console.log('🔧 Router配置成功')

// Element Plus配置
app.use(ElementPlus)
console.log('🔧 Element Plus配置成功')

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
console.log('🔧 Element Plus图标注册成功')

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('🚨 Vue错误:', err, info)
}

// 挂载应用
try {
  app.mount('#app')
  console.log('✅ 应用挂载成功！')
} catch (error) {
  console.error('❌ 应用挂载失败:', error)

  // 显示错误页面
  document.body.innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif;">
      <h1>❌ 应用启动失败</h1>
      <p>错误信息: ${error.message}</p>
      <p>请打开浏览器控制台查看详细错误信息</p>
      <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
    </div>
  `
}

export default app
