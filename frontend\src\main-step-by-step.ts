/**
 * 逐步测试版本 - 用于排查问题
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

console.log('🔧 Step 1: 基础导入成功')

import App from './App.vue'
console.log('🔧 Step 2: App.vue 导入成功')

import router from './router'
console.log('🔧 Step 3: Router 导入成功')

// 尝试导入配置
try {
  const { config } = await import('@/config')
  console.log('🔧 Step 4: Config 导入成功', config)
} catch (error) {
  console.error('❌ Step 4: Config 导入失败', error)
}

// 尝试导入国际化
try {
  const i18n = await import('@/locales')
  console.log('🔧 Step 5: i18n 导入成功')
} catch (error) {
  console.error('❌ Step 5: i18n 导入失败', error)
}

// 尝试导入HTTP客户端
try {
  const http = await import('@/api/http')
  console.log('🔧 Step 6: HTTP 导入成功')
} catch (error) {
  console.error('❌ Step 6: HTTP 导入失败', error)
}

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
console.log('🔧 Step 7: Element Plus 导入成功')

// 导入Element Plus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
console.log('🔧 Step 8: Element Plus Icons 导入成功')

// 尝试导入样式文件
try {
  await import('@/assets/styles/element.scss')
  console.log('🔧 Step 9: Element 样式导入成功')
} catch (error) {
  console.error('❌ Step 9: Element 样式导入失败', error)
}

try {
  await import('@/assets/styles/index.scss')
  console.log('🔧 Step 10: Index 样式导入成功')
} catch (error) {
  console.error('❌ Step 10: Index 样式导入失败', error)
}

// 尝试导入ECharts
try {
  const echarts = await import('echarts')
  console.log('🔧 Step 11: ECharts 导入成功')
} catch (error) {
  console.error('❌ Step 11: ECharts 导入失败', error)
}

// 尝试导入全局组件
try {
  const GlobalComponents = await import('@/components')
  console.log('🔧 Step 12: 全局组件导入成功')
} catch (error) {
  console.error('❌ Step 12: 全局组件导入失败', error)
}

// 尝试导入错误处理
try {
  const { setupErrorHandler } = await import('./utils/error-handler')
  console.log('🔧 Step 13: 错误处理导入成功')
} catch (error) {
  console.error('❌ Step 13: 错误处理导入失败', error)
}

// 尝试导入安全模块
try {
  const { initSecurity } = await import('@/utils/security')
  console.log('🔧 Step 14: 安全模块导入成功')
} catch (error) {
  console.error('❌ Step 14: 安全模块导入失败', error)
}

// 尝试导入ResizeObserver修复
try {
  const { setupResizeObserverFix } = await import('@/utils/resize-observer-fix')
  console.log('🔧 Step 15: ResizeObserver修复导入成功')
} catch (error) {
  console.error('❌ Step 15: ResizeObserver修复导入失败', error)
}

console.log('🔧 所有导入测试完成，开始创建应用...')

// 创建Vue应用实例
const app = createApp(App)
console.log('🔧 Vue应用实例创建成功')

// Pinia状态管理配置
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)
console.log('🔧 Pinia配置成功')

// 路由配置
app.use(router)
console.log('🔧 Router配置成功')

// Element Plus配置
app.use(ElementPlus)
console.log('🔧 Element Plus配置成功')

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
console.log('🔧 Element Plus图标注册成功')

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('🚨 Vue错误:', err, info)
}

// 挂载应用
try {
  app.mount('#app')
  console.log('✅ 应用挂载成功！')
} catch (error) {
  console.error('❌ 应用挂载失败:', error)
  
  // 显示错误页面
  document.body.innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif;">
      <h1>❌ 应用启动失败</h1>
      <p>错误信息: ${error.message}</p>
      <p>请打开浏览器控制台查看详细错误信息</p>
      <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
    </div>
  `
}

export default app
