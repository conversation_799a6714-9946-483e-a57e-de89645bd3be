import { createApp } from 'vue'

// 最简单的测试应用
const TestApp = {
  template: `
    <div style="padding: 20px; font-family: Arial, sans-serif; background: #f0f0f0; min-height: 100vh;">
      <h1 style="color: #409EFF;">🎉 Vue 3 应用正常运行！</h1>
      <p><strong>当前时间:</strong> {{ currentTime }}</p>
      <p><strong>应用状态:</strong> 已成功挂载到 #app</p>
      <button @click="count++" style="padding: 8px 16px; background: #409EFF; color: white; border: none; border-radius: 4px; margin: 5px;">
        点击测试 ({{ count }})
      </button>
      <div style="margin-top: 20px; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>系统信息</h3>
        <ul>
          <li>Vue 版本: {{ vueVersion }}</li>
          <li>页面加载时间: {{ loadTime }}</li>
          <li>点击次数: {{ count }}</li>
        </ul>
      </div>
    </div>
  `,
  data() {
    return {
      count: 0,
      currentTime: new Date().toLocaleString(),
      vueVersion: '3.x',
      loadTime: new Date().toLocaleString()
    }
  },
  mounted() {
    console.log('✅ Vue应用已成功挂载')
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
  }
}

// 创建应用
const app = createApp(TestApp)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err, info)
  alert(`Vue错误: ${err.message}`)
}

// 挂载应用
try {
  app.mount('#app')
  console.log('🔧 调试应用已挂载到 #app')
} catch (error) {
  console.error('❌ 应用挂载失败:', error)
  document.body.innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif;">
      <h1>❌ 应用挂载失败</h1>
      <p>错误信息: ${error.message}</p>
      <p>请检查控制台获取更多信息</p>
    </div>
  `
}
