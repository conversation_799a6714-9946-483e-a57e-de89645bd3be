import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { unifiedMarketApi as marketApi } from '@/api/market_unified'
import http from '@/api/http'
import { useWebSocket } from '@/composables/useWebSocket'
import { marketWebSocketService } from '@/services/market-websocket.service'
import { unifiedCache, CacheType } from '@/utils/cache'
import type {
  QuoteData,
  KLineData,
  StockInfo,
  MarketOverview,
  SectorData,
  NewsData,
  SearchResult,
  WatchlistItem,
  TimePeriod,
  StockMarket,
  OrderBookData,
  FinancialData,
  RankingData,
  RankingType
} from '@/types/market'

export const useMarketStore = defineStore('market', () => {
  // ============ 状态定义 ============

  // 实时行情数据 Map<symbol, QuoteData>
  const quotes = ref(new Map<string, QuoteData>())

  // K线数据 Map<symbol-period, KLineData[]>
  const klineData = ref(new Map<string, KLineData[]>())

  // 搜索结果缓存
  const searchResults = ref<SearchResult[]>([])

  // 自选股列表
  const watchlist = ref<WatchlistItem[]>([])

  // 市场概览数据
  const marketOverview = ref<MarketOverview[]>([])

  // 板块数据
  const sectors = ref<SectorData[]>([])

  // 行业数据
  const industries = ref<Array<{ code: string; name: string }>>([])

  // 新闻资讯
  const news = ref<NewsData[]>([])

  // 指数数据
  const indices = ref<Record<string, { currentPrice: number; change: number; changePercent: number; volume?: number; amount?: number }>>({})

  // 热门股票
  const hotStocks = ref<Array<{ symbol: string; name: string; currentPrice: number; change: number; changePercent: number }>>([])

  // 股票列表
  const stockList = ref<Array<{ symbol: string; name: string; currentPrice: number; change: number; changePercent: number; volume: number; amount: number; industry: string; market: string; pinyin?: string }>>([])

  // 排行榜数据
  const rankings = ref(new Map<RankingType, RankingData[]>())

  // 订单簿数据
  const orderBooks = ref(new Map<string, OrderBookData>())

  // 财务数据缓存
  const financialData = ref(new Map<string, FinancialData[]>())

  // 当前选中的股票
  const selectedStock = ref<StockInfo | null>(null)

  // 当前选中的时间周期
  const selectedPeriod = ref<TimePeriod>('1d')

  // 当前选中的市场
  const selectedMarket = ref<StockMarket>('SH')

  // WebSocket订阅列表
  const subscriptions = ref(new Set<string>())

  // 加载状态
  const loading = ref({
    quotes: false,
    kline: false,
    search: false,
    watchlist: false,
    overview: false,
    sectors: false,
    news: false,
    rankings: false,
    orderbook: false,
    financial: false
  })

  // 错误状态
  const errors = ref({
    quotes: null as string | null,
    kline: null as string | null,
    search: null as string | null,
    watchlist: null as string | null,
    overview: null as string | null,
    sectors: null as string | null,
    news: null as string | null,
    rankings: null as string | null,
    orderbook: null as string | null,
    financial: null as string | null
  })

  // WebSocket实例
  const wsService = useWebSocket()

  // WebSocket连接状态
  const connected = computed(() => wsService.isConnected.value)

  // stocks是stockList的别名，用于兼容
  const stocks = computed(() => stockList.value)

  // ============ 计算属性 ============

  // 自选股行情数据
  const watchlistQuotes = computed(() => {
    return watchlist.value.map(item => ({
      ...item,
      quote: quotes.value.get(item.symbol) || null
    })).filter(item => item.quote !== null)
  })

  // 市场统计数据
  const marketStats = computed(() => {
    const totalUp = marketOverview.value.reduce((sum, market) => sum + market.upCount, 0)
    const totalDown = marketOverview.value.reduce((sum, market) => sum + market.downCount, 0)
    const totalFlat = marketOverview.value.reduce((sum, market) => sum + market.flatCount, 0)
    const total = totalUp + totalDown + totalFlat

    return {
      totalUp,
      totalDown,
      totalFlat,
      total,
      upPercent: total > 0 ? (totalUp / total) * 100 : 0,
      downPercent: total > 0 ? (totalDown / total) * 100 : 0
    }
  })

  // 涨幅榜前10
  const topGainers = computed(() => {
    return rankings.value.get('change_percent')?.slice(0, 10) || []
  })

  // 跌幅榜前10
  const topLosers = computed(() => {
    const changePercent = rankings.value.get('change_percent') || []
    return changePercent.slice(-10).reverse()
  })

  // 成交额榜前10
  const topTurnover = computed(() => {
    return rankings.value.get('turnover')?.slice(0, 10) || []
  })

  // 活跃板块前5
  const activeSectors = computed(() => {
    return sectors.value
      .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
      .slice(0, 5)
  })

  // 当前选中股票的行情
  const selectedStockQuote = computed(() => {
    return selectedStock.value ? quotes.value.get(selectedStock.value.symbol) : null
  })

  // 当前选中股票的K线数据
  const selectedStockKLine = computed(() => {
    if (!selectedStock.value) return []
    const key = `${selectedStock.value.symbol}-${selectedPeriod.value}`
    return klineData.value.get(key) || []
  })

  // ============ 行情数据方法 ============

  // 获取单只股票行情
  const fetchQuote = async (symbol: string) => {
    loading.value.quotes = true
    errors.value.quotes = null

    try {
      const quote = await marketApi.getQuote(symbol)
      quotes.value.set(symbol, quote)
      return quote
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取行情失败'
      errors.value.quotes = message
      throw error
    } finally {
      loading.value.quotes = false
    }
  }

  // 批量获取行情
  const fetchQuotes = async (symbols: string[]) => {
    loading.value.quotes = true
    errors.value.quotes = null

    try {
      const quotesData = await marketApi.getQuotes({ symbols })
      quotesData.forEach(quote => {
        quotes.value.set(quote.symbol, quote)
      })
      return quotesData
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取行情失败'
      errors.value.quotes = message
      throw error
    } finally {
      loading.value.quotes = false
    }
  }

  // 获取K线数据
  const fetchKLineData = async (symbol: string, period: TimePeriod = '1d', limit = 500) => {
    loading.value.kline = true
    errors.value.kline = null

    try {
      const data = await marketApi.getKLineData({ symbol, period, limit })
      const key = `${symbol}-${period}`
      klineData.value.set(key, data)
      return data
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取K线数据失败'
      errors.value.kline = message
      throw error
    } finally {
      loading.value.kline = false
    }
  }

  // 获取历史K线数据
  const fetchHistoryKLineData = async (
    symbol: string,
    period: TimePeriod,
    startTime: number,
    endTime: number
  ) => {
    loading.value.kline = true
    errors.value.kline = null

    try {
      const data = await marketApi.getKLineData({
        symbol,
        period,
        startTime,
        endTime,
        limit: 10000
      })
      const key = `${symbol}-${period}`
      klineData.value.set(key, data)
      return data
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取历史K线数据失败'
      errors.value.kline = message
      throw error
    } finally {
      loading.value.kline = false
    }
  }

  // 获取股票列表（占位实现，可根据市场/行业过滤）
  const fetchStockList = async (params: { market?: string; industry?: string; pageSize?: number } = {}) => {
    loading.value.quotes = true
    errors.value.quotes = null

    try {
      console.log('🚀 开始获取股票列表，参数:', params)

      // 使用marketApi获取股票列表（已包含模拟数据fallback）
      const list = await marketApi.getStockList(params)
      console.log('✅ 成功获取股票数据:', list.length, '只股票')

      // 显示前几只股票的详细信息
      if (list.length > 0) {
        console.log('📊 前3只股票详情:', list.slice(0, 3))
      }

      stockList.value = list
      console.log('💾 股票列表已保存到store，当前数量:', stockList.value.length)

      // 从股票列表中提取行业数据（如果行业数据为空）
      if (list.length > 0 && industries.value.length === 0) {
        const industrySet = new Set<string>()
        const industryList: Array<{ code: string; name: string }> = []

        list.forEach((stock: any) => {
          if (stock.industry && !industrySet.has(stock.industry)) {
            industrySet.add(stock.industry)
            industryList.push({
              code: stock.industry,
              name: stock.industry
            })
          }
        })

        if (industryList.length > 0) {
          industries.value = industryList
          console.log('Industries extracted from stocks:', industryList)
        }
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取股票列表失败'
      errors.value.quotes = message
      console.error('❌ fetchStockList error:', error)

      // 如果是认证错误，尝试重新登录
      if (error instanceof Error && error.message.includes('401')) {
        console.warn('🔐 认证失败，尝试重新登录...')
        // 这里可以触发重新登录逻辑
      }

      // API层已经处理了模拟数据fallback，这里不需要额外处理
      console.warn('⚠️ 获取股票列表失败')
      stockList.value = []

    } finally {
      loading.value.quotes = false
    }
  }

  // ============ 搜索相关方法 ============

  // 搜索股票
  const searchStocks = async (keyword: string, market?: StockMarket) => {
    if (!keyword.trim()) {
      searchResults.value = []
      return []
    }

    loading.value.search = true
    errors.value.search = null

    try {
      const results = await marketApi.searchStocks({ keyword, market, limit: 20 })
      searchResults.value = results
      return results
    } catch (error) {
      console.warn('⚠️ API搜索失败，使用本地数据搜索:', error)

      // 使用本地股票列表进行搜索
      const localResults = (stockList.value || []).filter(stock => {
        const query = keyword.toLowerCase()
        return stock.symbol.toLowerCase().includes(query) ||
               stock.name.toLowerCase().includes(query) ||
               stock.pinyin?.toLowerCase().includes(query)
      }).slice(0, 20)

      searchResults.value = localResults
      return localResults
    } finally {
      loading.value.search = false
    }
  }

  // 清空搜索结果
  const clearSearchResults = () => {
    searchResults.value = []
  }

  // ============ 自选股管理 ============

  // 获取自选股列表
  const fetchWatchlist = async () => {
    loading.value.watchlist = true
    errors.value.watchlist = null

    try {
      const data = await marketApi.getWatchlist()
      watchlist.value = data

      // 订阅自选股行情
      const symbols = data.map(item => item.symbol)
      if (symbols.length > 0) {
        await subscribeQuotes(symbols)
      }

      return data
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取自选股失败'
      errors.value.watchlist = message
      throw error
    } finally {
      loading.value.watchlist = false
    }
  }

  // 添加自选股
  const addToWatchlist = async (symbol: string, name: string) => {
    try {
      await marketApi.addToWatchlist(symbol)

      const newItem: WatchlistItem = {
        symbol,
        name,
        addTime: Date.now(),
        sort: watchlist.value.length
      }

      watchlist.value.push(newItem)

      // 订阅新股票的行情
      await subscribeQuote(symbol)

      return newItem
    } catch (error) {
      const message = error instanceof Error ? error.message : '添加自选股失败'
      throw new Error(message)
    }
  }

  // 从自选股移除
  const removeFromWatchlist = async (symbol: string) => {
    try {
      await marketApi.removeFromWatchlist(symbol)

      const index = watchlist.value.findIndex(item => item.symbol === symbol)
      if (index !== -1) {
        watchlist.value.splice(index, 1)
      }

      // 取消订阅
      await unsubscribeQuote(symbol)
    } catch (error) {
      const message = error instanceof Error ? error.message : '移除自选股失败'
      throw new Error(message)
    }
  }

  // 检查是否在自选股中
  const isInWatchlist = (symbol: string): boolean => {
    return watchlist.value.some(item => item.symbol === symbol)
  }

  // ============ 市场概览方法 ============

  // 获取市场概览
  const fetchMarketOverview = async () => {
    loading.value.overview = true
    errors.value.overview = null

    try {
      // 调用真实API获取数据 - 使用统一的API路径
      const response = await http.get('/api/v1/market/overview')
      const data = response.data

      // 处理指数数据 - 兼容多种API格式
      const processedData = this._normalizeMarketOverviewData(data)

      if (processedData && processedData.indices && Array.isArray(processedData.indices) && processedData.indices.length > 0) {
        const indexData: Record<string, any> = {}

        console.log('🔍 标准化后的指数数据:', processedData.indices)

        processedData.indices.forEach((index: any) => {
          const symbol = index.code || index.symbol || index.name
          indexData[symbol] = {
            currentPrice: index.current || index.value || index.price || 0,
            change: index.change || 0,
            changePercent: index.change_percent || index.changePercent || 0,
            volume: index.volume || 0,
            amount: index.turnover || index.amount || 0,
            name: index.name
          }
        })
        indices.value = indexData
        console.log('✅ 成功处理指数数据:', Object.keys(indices.value).length, '个指数')
        console.log('📊 处理后的指数数据:', indices.value)
      } else {
        // 使用历史数据模式的指数数据
        console.log('📊 使用历史数据模式的指数数据')
        indices.value = this._getHistoricalIndicesData()
        console.log('✅ 使用历史指数数据:', Object.keys(indices.value).length, '个指数')
      }

      // 处理市场概览数据
      if (data.data && data.data.stats) {
        const stats = data.data.stats
        marketOverview.value = [
          {
            market: 'SH',
            name: '上海市场',
            upCount: Math.floor((stats.advancers || 0) / 2),
            downCount: Math.floor((stats.decliners || 0) / 2),
            flatCount: Math.floor((stats.unchanged || 0) / 2)
          },
          {
            market: 'SZ',
            name: '深圳市场',
            upCount: Math.ceil((stats.advancers || 0) / 2),
            downCount: Math.ceil((stats.decliners || 0) / 2),
            flatCount: Math.ceil((stats.unchanged || 0) / 2)
          }
        ]
      } else {
        // 使用默认值
        marketOverview.value = [
          {
            market: 'SH',
            name: '上海市场',
            upCount: 0,
            downCount: 0,
            flatCount: 0
          },
          {
            market: 'SZ',
            name: '深圳市场',
            upCount: 0,
            downCount: 0,
            flatCount: 0
          }
        ]
      }

      return { data }
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取市场概览失败'
      errors.value.overview = message
      console.warn('⚠️ API获取市场概览失败，使用模拟数据:', error)

      // 使用模拟指数数据
      indices.value = {
        '000001': {
          name: '上证指数',
          currentPrice: 3245.68,
          change: 12.45,
          changePercent: 0.38,
          volume: 245680000,
          amount: 3456789000
        },
        '399001': {
          name: '深证成指',
          currentPrice: 10856.23,
          change: -23.67,
          changePercent: -0.22,
          volume: 189450000,
          amount: 2789456000
        },
        '399006': {
          name: '创业板指',
          currentPrice: 2234.56,
          change: 8.90,
          changePercent: 0.40,
          volume: 156780000,
          amount: 1987654000
        },
        '000300': {
          name: '沪深300',
          currentPrice: 3987.45,
          change: 15.23,
          changePercent: 0.38,
          volume: 198765000,
          amount: 2876543000
        }
      }

      marketOverview.value = [
        { label: '上涨', value: 1245, color: '#f56c6c' },
        { label: '下跌', value: 987, color: '#67c23a' },
        { label: '平盘', value: 234, color: '#909399' }
      ]

      console.log('📊 使用模拟市场数据:', Object.keys(indices.value).length, '个指数')
    } finally {
      loading.value.overview = false
    }
  }

  // 获取指数名称的辅助函数
  const getIndexName = (symbol: string) => {
    const INDEX_MAP: Record<string, string> = {
      '000001.SH': '上证指数',
      '399001.SZ': '深证成指',
      '399006.SZ': '创业板指',
      '000688.SH': '科创50'
    }
    return INDEX_MAP[symbol] || symbol
  }

  const fetchHotStocks = async () => {
    try {
      const response = await http.get('/api/v1/market/hot_stocks')
      const data = response.data
      hotStocks.value = data.hot_stocks || []
    } catch (error) {
      console.error('获取热门股票失败:', error)
      hotStocks.value = []
    }
  }

  // 获取板块数据
  const fetchSectors = async () => {
    loading.value.sectors = true
    errors.value.sectors = null

    try {
      const response = await http.get('/api/v1/market/sectors')

      const data = response.data
      console.log('🔍 板块数据API响应:', data)

      // 处理Tushare API响应格式
      if (data.code === 200 && data.data && data.data.sectors) {
        sectors.value = data.data.sectors.map((sector: any) => ({
          code: sector.name, // 使用名称作为code
          name: sector.name,
          changePercent: sector.change_percent || sector.changePercent || 0,
          stockCount: sector.stock_count || sector.stockCount || 0,
          marketCap: sector.market_cap || sector.marketCap || 0
        }))
        console.log('✅ 成功处理板块数据:', sectors.value.length, '个板块')
      } else {
        // 只在开发环境显示数据格式警告
        if (import.meta.env.DEV) {
          console.warn('⚠️ 板块数据格式异常或为空:', data)
        }
        sectors.value = []
      }

      // 使用板块名称作为行业数据
      const industryList: Array<{ code: string; name: string }> = sectors.value.map(sector => ({
        code: sector.code,
        name: sector.name
      }))

      industries.value = industryList
      console.log('✅ 从板块数据提取行业信息:', industryList.length, '个行业')

      return sectors.value
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取板块数据失败'
      errors.value.sectors = message
      console.error('获取板块数据失败:', error)
      sectors.value = []
      industries.value = []
      throw error
    } finally {
      loading.value.sectors = false
    }
  }

  // 获取新闻资讯
  const fetchNews = async (limit = 20) => {
    loading.value.news = true
    errors.value.news = null

    try {
      const data = await marketApi.getNews({ limit })
      // 确保返回的是数组
      news.value = Array.isArray(data) ? data : []
      return news.value
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取新闻失败'
      errors.value.news = message
      console.warn('⚠️ 获取新闻失败，使用空数组:', error)
      // 失败时设置为空数组，避免组件错误
      news.value = []
      return []
    } finally {
      loading.value.news = false
    }
  }

  // 获取排行榜数据
  const fetchRankings = async (type: RankingType, limit = 50) => {
    loading.value.rankings = true
    errors.value.rankings = null

    try {
      const response = await http.get('/api/v1/market/rankings', {
        params: { type, limit }
      })
      const data = response.data
      console.log('🔍 排行榜API响应:', data)

      let rankingData = []

      // 处理Tushare API响应格式
      if (data.code === 200 && data.data && data.data.rankings) {
        rankingData = data.data.rankings.map((item: any) => ({
          rank: item.rank,
          symbol: item.symbol,
          name: item.name,
          currentPrice: item.current_price || item.currentPrice || 0,
          change: item.change || 0,
          changePercent: item.change_percent || item.changePercent || 0,
          volume: item.volume || 0,
          turnover: item.turnover || item.amount || 0
        }))
        console.log('✅ 成功处理排行榜数据:', rankingData.length, '个项目')
      } else {
        // 只在开发环境显示数据格式警告
        if (import.meta.env.DEV) {
          console.warn('⚠️ 排行榜数据格式异常或为空:', data)
        }
        rankingData = []
      }

      rankings.value.set(type, rankingData)
      return rankingData
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取排行榜失败'
      errors.value.rankings = message
      console.error('获取排行榜失败:', error)
      rankings.value.set(type, [])
      return []
    } finally {
      loading.value.rankings = false
    }
  }

  // 获取订单簿数据
  const fetchOrderBook = async (symbol: string) => {
    loading.value.orderbook = true
    errors.value.orderbook = null

    try {
      const data = await marketApi.getOrderBook(symbol)
      orderBooks.value.set(symbol, data)
      return data
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取订单簿失败'
      errors.value.orderbook = message
      throw error
    } finally {
      loading.value.orderbook = false
    }
  }

  // 获取财务数据
  const fetchFinancialData = async (symbol: string) => {
    loading.value.financial = true
    errors.value.financial = null

    try {
      const data = await marketApi.getFinancialData(symbol)
      financialData.value.set(symbol, data)
      return data
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取财务数据失败'
      errors.value.financial = message
      throw error
    } finally {
      loading.value.financial = false
    }
  }

  // ============ WebSocket实时数据 ============

  // 处理实时行情更新
  const handleTickUpdate = (tick: QuoteData) => {
    // 更新指数数据
    if (indices.value[tick.symbol]) {
      indices.value[tick.symbol] = {
        ...indices.value[tick.symbol],
        currentPrice: tick.currentPrice,
        change: tick.change,
        changePercent: tick.changePercent,
        volume: tick.volume,
        amount: tick.turnover
      }
    }

    // 更新股票列表
    const index = stockList.value.findIndex(stock => stock.symbol === tick.symbol)
    if (index !== -1) {
      stockList.value[index] = { ...stockList.value[index], ...tick }
    }

    // 更新行情缓存
    quotes.value.set(tick.symbol, tick)
  }

  // 连接WebSocket
  const connectWebSocket = async () => {
    try {
      // 检查websocketService是否存在
      if (!wsService.websocketService) {
        throw new Error('WebSocket服务未初始化')
      }

      await wsService.websocketService.connect()

      // 监听实时行情推送
      wsService.websocketService.on('tick', handleTickUpdate)
      wsService.websocketService.on('market_tick', handleTickUpdate)
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      throw error // 抛出错误以便上层处理
    }
  }

  // 订阅单只股票行情
  const subscribeQuote = async (symbol: string) => {
    if (connected.value) {
      wsService.subscribeMarketTick([symbol], (data) => {
        quotes.value.set(data.symbol, data)
      })
      subscriptions.value.add(`quote:${symbol}`)
    }
  }

  // 订阅多只股票行情
  const subscribeQuotes = async (symbols: string[]) => {
    if (connected.value) {
      for (const symbol of symbols) {
        await subscribeQuote(symbol)
      }
    }
  }

  // 取消订阅行情
  const unsubscribeQuote = async (symbol: string) => {
    // WebSocket服务会自动处理取消订阅
    subscriptions.value.delete(`quote:${symbol}`)
  }

  // 订阅K线数据
  const subscribeKLine = async (symbol: string, period: TimePeriod) => {
    if (connected.value) {
      wsService.subscribeMarketKline(symbol, period, (data) => {
        updateKLineData(symbol, period, data)
      })
      subscriptions.value.add(`kline:${symbol}:${period}`)
    }
  }

  // 取消订阅K线数据
  const unsubscribeKLine = async (symbol: string, period: TimePeriod) => {
    // WebSocket服务会自动处理取消订阅
    subscriptions.value.delete(`kline:${symbol}:${period}`)
  }

  // 订阅订单簿
  const subscribeOrderBook = async (symbol: string) => {
    if (connected.value) {
      // 这里需要实现订单簿订阅
      subscriptions.value.add(`orderbook:${symbol}`)
    }
  }

  // ============ 状态管理方法 ============

  // 设置选中股票
  const setSelectedStock = async (stock: StockInfo) => {
    selectedStock.value = stock

    // 获取该股票的行情和K线数据
    await Promise.all([
      fetchQuote(stock.symbol),
      fetchKLineData(stock.symbol, selectedPeriod.value)
    ])

    // 订阅实时数据
    await Promise.all([
      subscribeQuote(stock.symbol),
      subscribeKLine(stock.symbol, selectedPeriod.value)
    ])
  }

  // 设置时间周期
  const setPeriod = async (period: TimePeriod) => {
    const oldPeriod = selectedPeriod.value
    selectedPeriod.value = period

    if (selectedStock.value) {
      // 取消旧周期订阅
      await unsubscribeKLine(selectedStock.value.symbol, oldPeriod)

      // 获取新周期数据并订阅
      await fetchKLineData(selectedStock.value.symbol, period)
      await subscribeKLine(selectedStock.value.symbol, period)
    }
  }

  // 设置选中市场
  const setSelectedMarket = (market: StockMarket) => {
    selectedMarket.value = market
  }

  // 更新行情数据
  const updateQuote = (symbol: string, quote: Partial<QuoteData>) => {
    const existingQuote = quotes.value.get(symbol)
    if (existingQuote) {
      quotes.value.set(symbol, { ...existingQuote, ...quote })
    }
  }

  // 更新K线数据
  const updateKLineData = (symbol: string, period: TimePeriod, newData: KLineData) => {
    const key = `${symbol}-${period}`
    const existingData = klineData.value.get(key) || []

    // 查找是否存在相同时间戳的数据
    const index = existingData.findIndex(k => k.timestamp === newData.timestamp)
    if (index !== -1) {
      existingData[index] = newData
    } else {
      existingData.push(newData)
      existingData.sort((a, b) => a.timestamp - b.timestamp)
    }

    klineData.value.set(key, existingData)
  }

  // ============ 初始化和清理 ============

  // 初始化市场数据
  const initialize = async () => {
    try {
      // 连接WebSocket (不阻塞其他数据加载)
      connectWebSocket().catch(err => {
        console.warn('WebSocket连接失败，将使用轮询模式:', err)
        // 设置轮询模式获取实时数据
        setInterval(() => {
          if (watchlist.value.length > 0) {
            const symbols = watchlist.value.map(item => item.symbol)
            fetchQuotes(symbols).catch(console.error)
          }
        }, 3000) // 每3秒更新一次
      })

      // 获取基础数据
      await Promise.allSettled([
        fetchMarketOverview(),
        fetchWatchlist(),
        fetchSectors(),
        fetchNews(10),
        fetchRankings('change_percent'),
        fetchRankings('turnover'),
        fetchStockList({ pageSize: 100 }) // 获取更多股票数据以提取行业信息
      ])
    } catch (error) {
      console.error('初始化市场数据失败:', error)
      // 不抛出错误，允许页面继续运行
    }
  }

  // 刷新数据
  const refresh = async () => {
    try {
      await Promise.all([
        fetchMarketOverview(),
        fetchSectors(),
        // 刷新自选股行情
        fetchQuotes(watchlist.value.map(item => item.symbol))
      ])
    } catch (error) {
      console.error('刷新市场数据失败:', error)
      throw error
    }
  }

  // 清理所有订阅
  const cleanup = async () => {
    // 清理订阅记录
    subscriptions.value.clear()

    // WebSocket连接由服务统一管理，不需要手动断开
  }

  // 重置状态
  const reset = () => {
    quotes.value.clear()
    klineData.value.clear()
    searchResults.value = []
    watchlist.value = []
    marketOverview.value = []
    sectors.value = []
    news.value = []
    rankings.value.clear()
    orderBooks.value.clear()
    financialData.value.clear()
    selectedStock.value = null
    selectedPeriod.value = '1d'
    selectedMarket.value = 'SH'
    subscriptions.value.clear()

    // 重置加载状态
    Object.keys(loading.value).forEach(key => {
      loading.value[key as keyof typeof loading.value] = false
    })

    // 重置错误状态
    Object.keys(errors.value).forEach(key => {
      errors.value[key as keyof typeof errors.value] = null
    })
  }

  // 获取单个股票行情
  const getQuote = (symbol: string) => {
    return quotes.value.get(symbol) || null
  }

  // ============ 辅助方法 ============

  /**
   * 标准化市场概览数据格式
   */
  const _normalizeMarketOverviewData = (data: any): any => {
    // 如果已经是标准格式，直接返回
    if (data && data.indices && Array.isArray(data.indices)) {
      return data
    }

    // 处理旧格式的数据
    if (data && data.data) {
      return {
        timestamp: data.data.timestamp || new Date().toISOString(),
        market_status: data.data.market_status || 'TRADING',
        stats: data.data.stats || {
          advancers: data.data.up_count || 0,
          decliners: data.data.down_count || 0,
          unchanged: data.data.flat_count || 0,
          total: data.data.total_count || 0,
          total_volume: data.data.total_volume || 0,
          total_amount: data.data.total_amount || 0
        },
        indices: data.data.indices ? _convertIndicesToArray(data.data.indices) : []
      }
    }

    return null
  }

  /**
   * 将指数对象转换为数组格式
   */
  const _convertIndicesToArray = (indices: any): any[] => {
    if (Array.isArray(indices)) {
      return indices
    }

    if (!indices || typeof indices !== 'object') {
      return []
    }

    return Object.entries(indices).map(([code, data]: [string, any]) => ({
      code,
      name: data.name || code,
      current: data.value || data.current || data.currentPrice || 0,
      change: data.change || 0,
      change_percent: data.change_percent || data.changePercent || 0,
      volume: data.volume || 0,
      turnover: data.turnover || data.amount || 0
    }))
  }

  /**
   * 获取历史数据模式的指数数据
   */
  const _getHistoricalIndicesData = (): Record<string, any> => {
    const today = new Date().toISOString().split('T')[0]
    const seed = _hashCode(today)
    const random = _seededRandom(seed)

    return {
      '000001.SH': {
        name: '上证指数',
        currentPrice: _generateHistoricalValue(3245.68, random(), 2.0),
        change: _generateHistoricalChange(15, random()),
        changePercent: _generateHistoricalChangePercent(random()),
        volume: 245680000,
        amount: 345678900000
      },
      '399001.SZ': {
        name: '深证成指',
        currentPrice: _generateHistoricalValue(10856.34, random(), 2.0),
        change: _generateHistoricalChange(50, random()),
        changePercent: _generateHistoricalChangePercent(random()),
        volume: 189450000,
        amount: 278945600000
      },
      '399006.SZ': {
        name: '创业板指',
        currentPrice: _generateHistoricalValue(2234.56, random(), 3.0),
        change: _generateHistoricalChange(20, random()),
        changePercent: _generateHistoricalChangePercent(random()),
        volume: 156780000,
        amount: 198765400000
      },
      '000688.SH': {
        name: '科创50',
        currentPrice: _generateHistoricalValue(1045.23, random(), 2.5),
        change: _generateHistoricalChange(10, random()),
        changePercent: _generateHistoricalChangePercent(random()),
        volume: 98765000,
        amount: 123456700000
      }
    }
  }

  /**
   * 生成基于历史数据的值
   */
  const _generateHistoricalValue = (baseValue: number, randomValue: number, volatility: number): number => {
    const changePercent = (randomValue - 0.5) * volatility * 2
    return Number((baseValue * (1 + changePercent / 100)).toFixed(2))
  }

  /**
   * 生成历史变化值
   */
  const _generateHistoricalChange = (baseRange: number, randomValue: number): number => {
    return Number(((randomValue - 0.5) * baseRange * 2).toFixed(2))
  }

  /**
   * 生成历史变化百分比
   */
  const _generateHistoricalChangePercent = (randomValue: number): number => {
    return Number(((randomValue - 0.5) * 4).toFixed(2)) // -2% 到 +2%
  }

  /**
   * 哈希函数
   */
  const _hashCode = (str: string): number => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash)
  }

  /**
   * 基于种子的随机数生成器
   */
  const _seededRandom = (seed: number): () => number => {
    let currentSeed = seed
    return () => {
      currentSeed = (currentSeed * 9301 + 49297) % 233280
      return currentSeed / 233280
    }
  }

  // ============ 返回状态和方法 ============

  return {
    // 状态
    quotes,
    klineData,
    searchResults,
    watchlist,
    marketOverview,
    sectors,
    industries,
    news,
    indices,
    hotStocks,
    rankings,
    orderBooks,
    financialData,
    selectedStock,
    selectedPeriod,
    selectedMarket,
    subscriptions,
    loading,
    errors,
    connected,
    stockList,
    stocks,

    // 计算属性
    watchlistQuotes,
    marketStats,
    topGainers,
    topLosers,
    topTurnover,
    activeSectors,
    selectedStockQuote,
    selectedStockKLine,

    // 方法
    fetchQuote,
    fetchQuotes,
    fetchKLineData,
    fetchHistoryKLineData,
    searchStocks,
    clearSearchResults,
    fetchWatchlist,
    addToWatchlist,
    removeFromWatchlist,
    isInWatchlist,
    fetchMarketOverview,
    fetchHotStocks,
    fetchSectors,
    fetchNews,
    fetchRankings,
    fetchOrderBook,
    fetchFinancialData,
    getQuote,
    connectWebSocket,
    subscribeQuote,
    subscribeQuotes,
    unsubscribeQuote,
    subscribeKLine,
    unsubscribeKLine,
    subscribeOrderBook,
    setSelectedStock,
    setPeriod,
    setSelectedMarket,
    updateQuote,
    updateKLineData,
    initialize,
    refresh,
    cleanup,
    reset,
    fetchStockList,
    handleTickUpdate
  }
})

