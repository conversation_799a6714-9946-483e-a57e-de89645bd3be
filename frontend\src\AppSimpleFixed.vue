<template>
  <div id="app-wrapper" style="padding: 20px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; min-height: 100vh;">
    <h1>🚀 量化投资平台</h1>
    <p>✅ Vue应用已成功启动！</p>
    <div style="background: rgba(255, 255, 255, 0.1); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); max-width: 600px; margin: 20px auto;">
      <h2>🎯 系统状态</h2>
      <ul style="text-align: left; display: inline-block;">
        <li>✅ Vue 3 应用运行正常</li>
        <li>✅ 前端服务器：http://localhost:5174</li>
        <li>✅ 响应式数据绑定正常</li>
      </ul>
      <div style="margin-top: 20px;">
        <button @click="reload" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">重新加载</button>
      </div>
      <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
        <p>⏰ 当前时间：{{ currentTime }}</p>
        <p>📄 页面状态：正常运行</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

console.log('🔧 AppSimpleFixed.vue 开始加载...')

const currentTime = ref('')

const updateTime = () => {
  try {
    currentTime.value = new Date().toLocaleString('zh-CN')
    console.log('⏰ 时间更新成功')
  } catch (error) {
    console.error('❌ 时间更新失败:', error)
  }
}

const reload = () => {
  console.log('🔄 重新加载页面...')
  window.location.reload()
}

onMounted(() => {
  console.log('🎉 AppSimpleFixed 组件开始挂载...')
  try {
    updateTime()
    setInterval(updateTime, 1000)
    console.log('✅ Vue 3 应用启动成功！')
    console.log('📊 AppSimpleFixed 主应用页面加载完成')
  } catch (error) {
    console.error('❌ 组件挂载过程中出错:', error)
  }
})

console.log('✅ AppSimpleFixed.vue 脚本加载完成')
</script>

<style>
#app-wrapper {
  width: 100%;
  min-height: 100vh;
}
</style>