import { createApp, h } from 'vue'

console.log('🔧 开始创建超简单Vue应用...')

// 使用渲染函数而不是模板
const UltraSimpleApp = {
  data() {
    return {
      message: '🎉 Vue应用正常运行！',
      count: 0,
      currentTime: new Date().toLocaleString()
    }
  },
  mounted() {
    console.log('✅ 超简单应用挂载成功')
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
  },
  methods: {
    increment() {
      this.count++
      console.log('点击计数:', this.count)
    },
    async testBackend() {
      try {
        const response = await fetch('http://localhost:8000/api/v1/health')
        if (response.ok) {
          const data = await response.json()
          alert('后端连接成功: ' + data.status)
        } else {
          alert('后端连接失败: ' + response.status)
        }
      } catch (error) {
        alert('后端连接错误: ' + error.message)
      }
    }
  },
  render() {
    return h('div', {
      style: {
        padding: '20px',
        fontFamily: 'Arial, sans-serif',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    }, [
      h('div', {
        style: {
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '40px',
          borderRadius: '15px',
          backdropFilter: 'blur(10px)',
          textAlign: 'center',
          maxWidth: '600px'
        }
      }, [
        h('h1', { style: { color: '#409EFF', margin: '0 0 20px 0' } }, this.message),
        h('p', { style: { fontSize: '16px', margin: '10px 0' } }, [
          h('strong', '当前时间: '),
          this.currentTime
        ]),
        h('p', { style: { fontSize: '16px', margin: '10px 0' } }, [
          h('strong', '应用状态: '),
          '已成功挂载'
        ]),
        h('div', { style: { margin: '20px 0' } }, [
          h('button', {
            onClick: this.increment,
            style: {
              padding: '12px 24px',
              background: '#409EFF',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              margin: '5px',
              fontSize: '14px',
              fontWeight: 'bold'
            }
          }, '点击测试 (' + this.count + ')'),
          h('button', {
            onClick: this.testBackend,
            style: {
              padding: '12px 24px',
              background: '#67C23A',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              margin: '5px',
              fontSize: '14px',
              fontWeight: 'bold'
            }
          }, '测试后端连接'),
          h('button', {
            onClick: () => window.location.reload(),
            style: {
              padding: '12px 24px',
              background: '#E6A23C',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              margin: '5px',
              fontSize: '14px',
              fontWeight: 'bold'
            }
          }, '重新加载')
        ]),
        h('div', {
          style: {
            background: 'rgba(0,0,0,0.3)',
            padding: '15px',
            borderRadius: '8px',
            marginTop: '20px',
            textAlign: 'left',
            fontSize: '12px',
            fontFamily: 'monospace'
          }
        }, [
          h('strong', '系统状态:'),
          h('br'),
          '✅ Vue 3 应用运行正常',
          h('br'),
          '✅ 渲染函数工作正常',
          h('br'),
          '✅ 响应式数据正常',
          h('br'),
          '✅ 前端服务器: http://localhost:5173',
          h('br'),
          '✅ 后端API: http://localhost:8000'
        ])
      ])
    ])
  }
}

try {
  // 创建Vue应用实例
  const app = createApp(UltraSimpleApp)
  console.log('✅ Vue应用实例创建成功')

  // 挂载应用
  app.mount('#app')
  console.log('✅ 应用挂载成功')

  console.log('🚀 超简单量化投资平台启动成功!')

} catch (error) {
  console.error('❌ 应用启动失败:', error)
  console.error('❌ 错误堆栈:', error.stack)
  
  // 显示错误页面
  document.body.innerHTML = '<div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;"><div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 600px;"><h1>❌ 应用启动失败</h1><p><strong>错误信息:</strong> ' + error.message + '</p><button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px;">重新加载</button></div></div>'
}
