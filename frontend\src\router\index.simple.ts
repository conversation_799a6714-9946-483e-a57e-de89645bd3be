import { createRouter, createWebHistory } from 'vue-router'

// 简化的路由配置，避免复杂的组件导入
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 根路径重定向到欢迎页
    {
      path: '/',
      redirect: '/welcome'
    },
    // 欢迎页面 - 应用入口
    {
      path: '/welcome',
      name: 'Welcome',
      component: () => import('@/views/Welcome/WelcomeView.vue').catch(() => {
        console.error('WelcomeView组件加载失败')
        // 如果Welcome页面也加载失败，返回一个简单的组件
        return {
          template: `
            <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; font-family: Arial, sans-serif;">
              <div style="background: rgba(255, 255, 255, 0.1); padding: 40px; border-radius: 15px; backdrop-filter: blur(10px);">
                <h1>🚀 量化投资平台</h1>
                <p>系统正在加载中...</p>
                <button @click="reload" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px;">重新加载</button>
              </div>
            </div>
          `,
          methods: {
            reload() {
              window.location.reload()
            }
          }
        }
      }),
      meta: {
        title: '欢迎',
        requiresAuth: false
      }
    },
    // 简单测试页面
    {
      path: '/test',
      name: 'Test',
      component: () => import('@/views/Test/SimpleTest.vue').catch(() => {
        console.error('SimpleTest组件加载失败')
        return {
          template: `
            <div style="padding: 20px;">
              <h1>测试页面</h1>
              <p>如果您看到这个页面，说明路由系统工作正常。</p>
              <button @click="goHome">返回首页</button>
            </div>
          `,
          methods: {
            goHome() {
              this.$router.push('/welcome')
            }
          }
        }
      }),
      meta: {
        title: '测试',
        requiresAuth: false
      }
    },
    // 使用DefaultLayout的完整应用（仅在需要时加载）
    {
      path: '/app',
      component: () => import('@/layouts/DefaultLayout.vue').catch(() => {
        console.error('DefaultLayout组件加载失败')
        // 如果布局组件加载失败，重定向到欢迎页
        router.push('/welcome')
        return { template: '<div>Loading...</div>' }
      }),
      children: [
        // 仪表盘
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard/DashboardView.vue').catch(() => {
            console.error('Dashboard组件加载失败')
            return { template: '<div>仪表盘加载失败</div>' }
          }),
          meta: {
            title: '仪表盘',
            requiresAuth: true
          }
        },
        // 行情中心
        {
          path: 'market',
          name: 'Market',
          redirect: '/app/market/realtime'
        },
        {
          path: 'market/realtime',
          name: 'RealTimeMarket',
          component: () => import('@/views/Market/RealTimeMarket.vue').catch(() => {
            console.error('RealTimeMarket组件加载失败')
            return { template: '<div>实时行情加载失败</div>' }
          }),
          meta: {
            title: '实时行情',
            requiresAuth: true
          }
        },
        {
          path: 'market/historical',
          name: 'HistoricalData',
          component: () => import('@/views/Market/EnhancedHistoricalData.vue').catch(() => {
            console.error('HistoricalData组件加载失败')
            return { template: '<div>历史数据加载失败</div>' }
          }),
          meta: {
            title: '历史数据',
            requiresAuth: true
          }
        }
      ]
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: {
        template: `
          <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; text-align: center; font-family: Arial, sans-serif;">
            <div>
              <h1>404 - 页面未找到</h1>
              <p>您访问的页面不存在</p>
              <button @click="goHome" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">返回首页</button>
            </div>
          </div>
        `,
        methods: {
          goHome() {
            this.$router.push('/welcome')
          }
        }
      },
      meta: {
        title: '页面未找到',
        requiresAuth: false
      }
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 简化的路由守卫
router.beforeEach(async (to, from, next) => {
  try {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - 量化投资平台`
    } else {
      document.title = '量化投资平台'
    }

    // 对于需要认证的页面，暂时跳过认证检查（开发阶段）
    if (to.meta?.requiresAuth) {
      console.log('路由需要认证，但在开发阶段跳过认证检查')
    }

    next()
  } catch (error) {
    console.error('路由守卫执行失败:', error)
    next('/welcome')
  }
})

// 全局后置钩子
router.afterEach((to, from) => {
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
})

// 错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  // 如果路由发生错误，尝试重定向到欢迎页
  router.push('/welcome').catch(() => {
    // 如果连重定向都失败了，刷新页面
    window.location.href = '/welcome'
  })
})

export default router