/**
 * 逐步调试版本 - 找出问题模块
 */

console.log('🔧 Step 1: 开始加载main.ts')

try {
  console.log('🔧 Step 2: 导入Vue...')
  const { createApp } = await import('vue')
  console.log('✅ Step 2: Vue导入成功')

  console.log('🔧 Step 3: 导入Pinia...')
  const { createPinia } = await import('pinia')
  const piniaPluginPersistedstate = await import('pinia-plugin-persistedstate')
  console.log('✅ Step 3: Pinia导入成功')

  console.log('🔧 Step 4: 导入App.vue...')
  const App = await import('./App.vue')
  console.log('✅ Step 4: App.vue导入成功')

  console.log('🔧 Step 5: 导入router...')
  const router = await import('./router')
  console.log('✅ Step 5: router导入成功')

  console.log('🔧 Step 6: 导入config...')
  const { config } = await import('@/config')
  console.log('✅ Step 6: config导入成功')

  console.log('🔧 Step 7: 导入i18n...')
  const i18n = await import('@/locales')
  console.log('✅ Step 7: i18n导入成功')

  console.log('🔧 Step 8: 导入Element Plus...')
  const ElementPlus = await import('element-plus')
  await import('element-plus/dist/index.css')
  await import('element-plus/theme-chalk/dark/css-vars.css')
  console.log('✅ Step 8: Element Plus导入成功')

  console.log('🔧 Step 9: 导入Element Plus图标...')
  const ElementPlusIconsVue = await import('@element-plus/icons-vue')
  console.log('✅ Step 9: Element Plus图标导入成功')

  console.log('🔧 Step 10: 导入全局组件...')
  const GlobalComponents = await import('@/components')
  console.log('✅ Step 10: 全局组件导入成功')

  console.log('🔧 Step 11: 导入错误处理...')
  const { setupErrorHandler } = await import('./utils/error-handler')
  console.log('✅ Step 11: 错误处理导入成功')

  console.log('🔧 Step 12: 创建Vue应用实例...')
  const app = createApp(App.default)
  console.log('✅ Step 12: Vue应用实例创建成功')

  console.log('🔧 Step 13: 配置Pinia...')
  const pinia = createPinia()
  pinia.use(piniaPluginPersistedstate.default)
  app.use(pinia)
  console.log('✅ Step 13: Pinia配置成功')

  console.log('🔧 Step 14: 配置路由...')
  app.use(router.default)
  console.log('✅ Step 14: 路由配置成功')

  console.log('🔧 Step 15: 配置Element Plus...')
  app.use(ElementPlus.default)
  console.log('✅ Step 15: Element Plus配置成功')

  console.log('🔧 Step 16: 配置国际化...')
  app.use(i18n.default)
  console.log('✅ Step 16: 国际化配置成功')

  console.log('🔧 Step 17: 注册Element Plus图标...')
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  console.log('✅ Step 17: Element Plus图标注册成功')

  console.log('🔧 Step 18: 注册全局组件...')
  app.use(GlobalComponents.default)
  console.log('✅ Step 18: 全局组件注册成功')

  console.log('🔧 Step 19: 设置错误处理...')
  setupErrorHandler(app)
  console.log('✅ Step 19: 错误处理设置成功')

  console.log('🔧 Step 20: 配置全局属性...')
  app.config.globalProperties.$config = config
  console.log('✅ Step 20: 全局属性配置成功')

  console.log('🔧 Step 21: 挂载应用...')
  app.mount('#app')
  console.log('✅ Step 21: 应用挂载成功')

  console.log(`🚀 ${config.app.name} 启动成功!`)
  console.log(`🌍 运行环境: ${config.app.isDev ? '开发' : '生产'}`)
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`)

} catch (error) {
  console.error('❌ 应用启动失败:', error)
  console.error('❌ 错误堆栈:', error.stack)
  
  // 显示错误页面
  document.body.innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif;">
      <h1>❌ 应用启动失败</h1>
      <p><strong>错误信息:</strong> ${error.message}</p>
      <details style="margin-top: 10px;">
        <summary style="cursor: pointer; font-weight: bold;">查看错误堆栈</summary>
        <pre style="background: rgba(0,0,0,0.1); padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 12px; overflow: auto;">${error.stack}</pre>
      </details>
      <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px;">重新加载</button>
    </div>
  `
}
