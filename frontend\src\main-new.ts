import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入Element Plus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 恢复token（如果页面刷新） - 暂时注释掉避免错误
// import { restoreTokenOnPageLoad } from '@/utils/auth'

// 全局错误处理
const handleGlobalError = (error: any, source?: string) => {
  console.error(`全局错误 [${source || 'unknown'}]:`, error)
  // 这里可以添加错误上报逻辑
}

window.addEventListener('error', (event) => {
  handleGlobalError(event.error, 'window.error')
})

window.addEventListener('unhandledrejection', (event) => {
  handleGlobalError(event.reason, 'unhandledrejection')
  event.preventDefault() // 阻止默认的错误处理
})

// 恢复认证状态 - 暂时注释掉避免错误
try {
  // restoreTokenOnPageLoad()
  console.log('跳过认证状态恢复（开发模式）')
} catch (error) {
  console.warn('恢复认证状态失败:', error)
}

// 创建Vue应用实例
const app = createApp(App)

// 全局错误处理器
app.config.errorHandler = (err, instance, info) => {
  handleGlobalError(err, `vue.${info}`)
  // 不要阻断应用继续运行
  return false
}

// 初始化应用
const initializeApp = async () => {
  try {
    // Pinia状态管理配置
    console.log('🔧 配置Pinia...')
    const pinia = createPinia()
    pinia.use(piniaPluginPersistedstate)
    app.use(pinia)

    // 路由配置
    console.log('🔧 配置路由...')
    app.use(router)

    // Element Plus配置
    console.log('🔧 配置Element Plus...')
    app.use(ElementPlus)

    // 注册Element Plus图标
    console.log('🔧 注册图标...')
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }

    // 等待路由准备就绪
    await router.isReady()
    console.log('🔧 路由准备就绪')

    // 挂载应用
    console.log('🔧 挂载应用...')
    app.mount('#app')

    console.log('✅ 应用启动成功')
    return true
  } catch (error) {
    console.error('❌ 应用启动失败:', error)
    showErrorPage(error)
    return false
  }
}

// 显示错误页面
const showErrorPage = (error: any) => {
  const errorMessage = error?.message || '未知错误'
  const errorStack = error?.stack || ''

  document.body.innerHTML = `
    <div style="
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: white;
      text-align: center;
      padding: 20px;
    ">
      <div style="
        background: rgba(255, 255, 255, 0.1);
        padding: 40px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        max-width: 600px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      ">
        <h1 style="margin: 0 0 20px 0; font-size: 2em;">❌ 应用启动失败</h1>
        <p style="margin: 0 0 15px 0; font-size: 1.1em;">应用在启动过程中遇到了错误，请尝试以下解决方案：</p>
        <div style="text-align: left; margin: 20px 0;">
          <p>• 检查网络连接</p>
          <p>• 清除浏览器缓存</p>
          <p>• 刷新页面重试</p>
        </div>
        <details style="margin: 20px 0; text-align: left;">
          <summary style="cursor: pointer; font-weight: bold;">查看错误详情</summary>
          <pre style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 12px; overflow: auto;">${errorMessage}
${errorStack}</pre>
        </details>
        <button onclick="window.location.reload()" style="
          background: #4CAF50;
          color: white;
          padding: 12px 24px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 16px;
          margin: 10px;
          transition: background-color 0.3s;
        ">重新加载页面</button>
        <button onclick="window.location.href='/welcome'" style="
          background: #2196F3;
          color: white;
          padding: 12px 24px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 16px;
          margin: 10px;
          transition: background-color 0.3s;
        ">返回首页</button>
      </div>
    </div>
  `
}

// 启动应用
initializeApp().catch(error => {
  console.error('应用初始化失败:', error)
})
