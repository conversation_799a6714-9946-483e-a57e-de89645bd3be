<template>
  <div id="app-wrapper">
    <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; min-height: 100vh; text-align: center;">
      <h1>🚀 量化投资平台</h1>
      <p>✅ Vue应用已成功启动！</p>
      <div style="background: rgba(255, 255, 255, 0.1); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); max-width: 600px; margin: 20px auto;">
        <h2>🎯 系统状态</h2>
        <ul style="text-align: left; display: inline-block;">
          <li>✅ Vue 3 应用运行正常</li>
          <li>✅ 前端服务器：http://localhost:5173</li>
          <li>✅ 后端API：http://localhost:8000</li>
        </ul>
        <div style="margin-top: 20px;">
          <button @click="testAPI" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">测试API连接</button>
          <button @click="reload" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">重新加载</button>
        </div>
        <div v-if="apiStatus" style="margin-top: 20px; padding: 15px; border-radius: 5px;" :style="{ background: apiStatus.success ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)' }">
          {{ apiStatus.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const apiStatus = ref(null)

// 测试API连接
async function testAPI() {
  try {
    const response = await fetch('http://localhost:8000/api/v1/health')
    const data = await response.json()
    apiStatus.value = {
      success: true,
      message: '✅ API连接成功！状态：' + data.status
    }
  } catch (error) {
    apiStatus.value = {
      success: false,
      message: '❌ API连接失败：' + error.message
    }
  }
}

// 重新加载页面
function reload() {
  window.location.reload()
}
</script>

<style lang="scss">
#app-wrapper {
  width: 100%;
  min-height: 100vh;
  height: auto !important;
  overflow: visible;
}

/* 全局布局修复 */
:deep(.default-layout) {
  height: auto !important;
  min-height: 100vh !important;
}

:deep(.layout-content) {
  height: auto !important;
  overflow: visible !important;
}

:deep(.market-content) {
  height: auto !important;
  overflow-x: hidden !important;
  overflow-y: visible !important;
}
</style>
