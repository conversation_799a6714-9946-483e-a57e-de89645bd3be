<template>
  <div id="app-wrapper">
    <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; min-height: 100vh; text-align: center;">
      <h1>🚀 量化投资平台</h1>
      <p>✅ Vue应用已成功启动！</p>
      <p>当前时间：{{ currentTime }}</p>
      <button @click="updateTime" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">更新时间</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const currentTime = ref('')

// 更新时间函数
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
  console.log('时间已更新:', currentTime.value)
}

// 组件挂载时执行
onMounted(() => {
  console.log('✅ 简化版 Vue 组件已挂载')
  updateTime()
  
  // 每秒更新时间
  setInterval(updateTime, 1000)
})

console.log('🔧 简化版 App.vue 脚本已加载')
</script>

<style>
#app-wrapper {
  width: 100%;
  min-height: 100vh;
}
</style>