/**
 * 超简化的量化投资平台入口文件 - 不使用路由和store
 */
import { createApp } from 'vue'
import App from './App.vue'

console.log('🔧 开始启动量化投资平台...')

// 创建Vue应用实例
const app = createApp(App)

try {
  console.log('✅ Vue应用实例创建成功')

  // 挂载应用
  app.mount('#app')
  console.log('✅ 应用挂载成功')

  console.log('🚀 量化投资平台启动成功!')
  console.log('⏰ 启动时间:', new Date().toLocaleString('zh-CN'))

} catch (error) {
  console.error('❌ 应用启动失败:', error)
  console.error('❌ 错误堆栈:', error.stack)

  // 显示错误页面
  document.body.innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
      <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 600px;">
        <h1>❌ 量化投资平台启动失败</h1>
        <p><strong>错误信息:</strong> ${error.message}</p>
        <details style="margin-top: 15px;">
          <summary style="cursor: pointer; font-weight: bold;">查看错误堆栈</summary>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 12px; overflow: auto;">${error.stack}</pre>
        </details>
        <button onclick="window.location.reload()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px;">重新加载</button>
      </div>
    </div>
  `
}

// 导出应用实例
export default app
