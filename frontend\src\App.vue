<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <div id="app-wrapper">
    <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; min-height: 100vh;">
      <h1>🚀 量化投资平台</h1>
      <p>✅ Vue应用已成功启动！</p>
      <RouterView />
    </div>
  </div>
</template>

<style lang="scss">
#app-wrapper {
  width: 100%;
  min-height: 100vh;
  height: auto !important;
  overflow: visible;
}

/* 全局布局修复 */
:deep(.default-layout) {
  height: auto !important;
  min-height: 100vh !important;
}

:deep(.layout-content) {
  height: auto !important;
  overflow: visible !important;
}

:deep(.market-content) {
  height: auto !important;
  overflow-x: hidden !important;
  overflow-y: visible !important;
}
</style>
