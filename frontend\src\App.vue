<template>
  <div id="app">
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition name="fade" mode="out-in">
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 监听路由变化，更新页面标题
watch(
  () => route.meta.title,
  (title) => {
    document.title = title ? `${title} - 量化投资平台` : '量化投资平台'
  },
  { immediate: true }
)

// 全局错误边界
window.addEventListener('error', (event) => {
  console.error('🚨 全局错误:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 未处理的Promise错误:', event.reason)
})
</script>

<style lang="scss">
// 全局样式
#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  min-height: 100vh;
  background: #f8f9fa;
}

// 路由切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 响应式布局修复
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

// 修复Element Plus样式冲突
.el-loading-mask {
  z-index: 3000 !important;
}

.el-message {
  z-index: 3001 !important;
}

.el-notification {
  z-index: 3002 !important;
}

// 防止布局抖动
.layout-content {
  min-height: 100vh;
}
</style>