<script setup lang="ts">
import { RouterView } from 'vue-router'
import { computed } from 'vue'
import PageLoading from '@/components/common/PageLoading.vue'
import { globalLoading } from '@/composables/core/usePageLoading'

// UI状态 - 简化版本，避免依赖不存在的store
const isDarkMode = computed(() => false)

// 全局加载状态
const { isLoading, loadingText, loadingTips } = globalLoading
</script>

<template>
  <div id="app-wrapper" :class="{ 'dark': isDarkMode }">
    <RouterView />
    <!-- 全局加载指示器 -->
    <PageLoading 
      :show="isLoading" 
      :text="loadingText" 
      :tips="loadingTips"
    />
  </div>
</template>

<style lang="scss">
#app-wrapper {
  width: 100%;
  min-height: 100vh;
  height: auto !important;
  overflow: visible;
}

/* 全局布局修复 */
:deep(.default-layout) {
  height: auto !important;
  min-height: 100vh !important;
}

:deep(.layout-content) {
  height: auto !important;
  overflow: visible !important;
}

:deep(.market-content) {
  height: auto !important;
  overflow-x: hidden !important;
  overflow-y: visible !important;
}
</style>
